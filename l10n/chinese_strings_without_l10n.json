{"strings_by_file": {"app/src/main/java/com/buque/wakoo/bean/Dressup.kt": [{"context": "if (expireTimestamp != null) \"${DateTimeUtils.formatDate(expireTimestamp!!, \"yyyy-MM-dd\")}到期\" else \"永久期限\"", "line": 193, "text": ")}到期", "type": "string_literal"}, {"context": "if (expireTimestamp != null) \"${DateTimeUtils.formatDate(expireTimestamp!!, \"yyyy-MM-dd\")}到期\" else \"永久期限\"", "line": 193, "text": "永久期限", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/bean/Gift.kt": [{"context": "\"name\": \"空中花园\",", "line": 216, "text": "空中花园", "type": "string_literal"}, {"context": "\"action_btn_txt\": \"赠送/求打赏/打赏礼物\",", "line": 224, "text": "赠送/求打赏/打赏礼物", "type": "string_literal"}, {"context": "\"cant_action_hint\": \"该礼物已下线\", # can_give为False时，按钮点击提示，可能为\"\", 为\"\"则点击无反应", "line": 226, "text": "该礼物已下线", "type": "string_literal"}, {"context": "\"cant_action_hint\": \"该礼物已下线\", # can_give为False时，按钮点击提示，可能为\"\", 为\"\"则点击无反应", "line": 226, "text": ", 为", "type": "string_literal"}, {"context": "\"hint\": \"背包礼物可赠送\",", "line": 227, "text": "背包礼物可赠送", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/bean/LiveRoom.kt": [{"context": "title = \"你要约会吗?\",", "line": 81, "text": "你要约会吗?", "type": "string_literal"}, {"context": "desc = \"大家来约会吧\",", "line": 85, "text": "大家来约会吧", "type": "string_literal"}, {"context": "notice = \"想要上麦请扣1\",", "line": 86, "text": "想要上麦请扣1", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/bean/PrivilegedGiftRemindBean.kt": [{"context": "\"nickname\": \"诗书塞外\",", "line": 18, "text": "诗书塞外", "type": "string_literal"}, {"context": "\"desc\": \"你还有背包礼物道具未使用\\n送个礼物给她吧，她会很开心哦\",", "line": 34, "text": "你还有背包礼物道具未使用\\n送个礼物给她吧，她会很开心哦", "type": "string_literal"}, {"context": "\"name\": \"情人礼盒\",", "line": 37, "text": "情人礼盒", "type": "string_literal"}, {"context": "\"btn_txt\": \"免费赠送\"", "line": 40, "text": "免费赠送", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/bean/VoiceData.kt": [{"context": "title = \"有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話\",", "line": 55, "text": "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 60, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 61, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 62, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈a\"),", "line": 63, "text": "哈哈a", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈哈\"),", "line": 64, "text": "哈哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 65, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 66, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 67, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈哈\"),", "line": 68, "text": "哈哈哈", "type": "string_literal"}, {"context": "title = \"有時話不多\",", "line": 151, "text": "有時話不多", "type": "string_literal"}, {"context": "desc = \"有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話\",", "line": 152, "text": "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 156, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 157, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 158, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈a\"),", "line": 159, "text": "哈哈a", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈哈\"),", "line": 160, "text": "哈哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 161, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 162, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈\"),", "line": 163, "text": "哈哈", "type": "string_literal"}, {"context": "VoiceTag(1, \"哈哈哈\"),", "line": 164, "text": "哈哈哈", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/bean/user/BasicUser.kt": [{"context": "name = \"小帅\",", "line": 92, "text": "小帅", "type": "string_literal"}, {"context": "\"幼儿园班花\",", "line": 102, "text": "幼儿园班花", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/consts/Pay.kt": [{"context": "*     (1, \"GOOGLE_IAP\", \"Google应用内购买\"),", "line": 19, "text": "Google应用内购买", "type": "string_literal"}, {"context": "*     (2, \"APPLE_IAP\", \"Apple应用内购买\"),", "line": 20, "text": "Apple应用内购买", "type": "string_literal"}, {"context": "*     (3, \"WXPAY\", \"微信支付\"),", "line": 21, "text": "微信支付", "type": "string_literal"}, {"context": "*     (4, \"ALIPAY\", \"支付宝\"),", "line": 22, "text": "支付宝", "type": "string_literal"}, {"context": "*     (6, \"JKO<PERSON>Y\", \"街口支付\"),  # 目前只支持台湾", "line": 24, "text": "街口支付", "type": "string_literal"}, {"context": "*     (1, \"GOOGLE_SDK\", \"谷歌SDK\"),", "line": 58, "text": "谷歌SDK", "type": "string_literal"}, {"context": "*     (2, \"APPLE_SDK\", \"苹果SDK\"),", "line": 59, "text": "苹果SDK", "type": "string_literal"}, {"context": "*     (3, \"WXSDK\", \"微信SDK\"),", "line": 60, "text": "微信SDK", "type": "string_literal"}, {"context": "*     (4, \"LINK_WEB\", \"浏览器打开链接\"),", "line": 61, "text": "浏览器打开链接", "type": "string_literal"}, {"context": "*     (5, \"LINK_WEB1\", \"浏览器打开链接(适配客户端)\"),", "line": 62, "text": "浏览器打开链接(适配客户端)", "type": "string_literal"}, {"context": "*     (6, \"LINK_WEBVIEW\", \"webview打开链接\"),", "line": 63, "text": "webview打开链接", "type": "string_literal"}, {"context": "*     (7, \"ORDER_LINK\", \"获取订单链接\"),", "line": 64, "text": "获取订单链接", "type": "string_literal"}, {"context": "*     (8, \"AGENT\", \"代充\"),", "line": 65, "text": "代充", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/consts/SceneType.kt": [{"context": "* (1, \"TRIBE\", \"部落\"),", "line": 6, "text": "部落", "type": "string_literal"}, {"context": "* (2, \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"语音房\"),", "line": 7, "text": "语音房", "type": "string_literal"}, {"context": "* (3, \"RPIVATERO<PERSON>\", \"私密小屋\")", "line": 8, "text": "私密小屋", "type": "string_literal"}, {"context": "* (4, \"RPIVATE_CHAT\", \"私聊\"),", "line": 9, "text": "私聊", "type": "string_literal"}, {"context": "* (5, \"MOMENT\", \"动态\"),", "line": 10, "text": "动态", "type": "string_literal"}, {"context": "* (6, \"PROFILE\", \"个人主页\"),", "line": 11, "text": "个人主页", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/core/pay/AppPayCoreKit.kt": [{"context": "showToast(\"创建订单失败，productId: $productId, orderType: ${request.orderType}\")", "line": 157, "text": "创建订单失败，productId: $productId, orderType: ${request.orderType}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/core/pay/GoogleBillingManager.kt": [{"context": "logI(\"google支付连接成功\")", "line": 158, "text": "google支付连接成功", "type": "string_literal"}, {"context": "reportBillingError(\"google支付服务不可用，code: ${billingResult.responseCode}\")", "line": 163, "text": "google支付服务不可用，code: ${billingResult.responseCode}", "type": "string_literal"}, {"context": "reportBillingError(\"google支付服务连接出错，code: ${billingResult.responseCode}\")", "line": 168, "text": "google支付服务连接出错，code: ${billingResult.responseCode}", "type": "string_literal"}, {"context": "LogUtils.e(\"ERR:不支持PRODUCT_DETAILS\")", "line": 267, "text": "ERR:不支持PRODUCT_DETAILS", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/core/webview/AppLinkNavigator.kt": [{"context": "throw IllegalAccessError(\"${rc.simpleName} 类未找到 companion.fromUri Method\")", "line": 73, "text": "${rc.simpleName} 类未找到 companion.fromUri Method", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/core/webview/offline/OfflinePkgManager.kt": [{"context": "logI(TAG, \"下载游戏成功[${game.name}]\")", "line": 250, "text": "下载游戏成功[${game.name}]", "type": "string_literal"}, {"context": "logI(TAG, \"开始解压游戏[${game.name}]\")", "line": 252, "text": "开始解压游戏[${game.name}]", "type": "string_literal"}, {"context": "\"解压游戏成功[${game.name}]=>文件夹${destGameZip.parentFile?.absolutePath}\",", "line": 261, "text": "解压游戏成功[${game.name}]=>文件夹${destGameZip.parentFile?.absolutePath}", "type": "string_literal"}, {"context": "logI(TAG, \"游戏配置写入成功[${game.name}]：$game\")", "line": 271, "text": "游戏配置写入成功[${game.name}]：$game", "type": "string_literal"}, {"context": "\"下载游戏失败[${game.name}]：\" + result.exceptionOrNull()?.message.orEmpty(),", "line": 275, "text": "下载游戏失败[${game.name}]：", "type": "string_literal"}, {"context": "logE(TAG, \"下载游戏失败：\" + e.message.orEmpty())", "line": 280, "text": "下载游戏失败：", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/game/core/Attributes.kt": [{"context": "override val name: String = \"重力属性\"", "line": 22, "text": "重力属性", "type": "string_literal"}, {"context": "override val desc: String = \"脚下必须有支撑点才不会下落\"", "line": 23, "text": "脚下必须有支撑点才不会下落", "type": "string_literal"}, {"context": "override val name: String = \"可食用属性\"", "line": 31, "text": "可食用属性", "type": "string_literal"}, {"context": "override val desc: String = \"可以被小蛇吃掉，改变蛇的长度\"", "line": 32, "text": "可以被小蛇吃掉，改变蛇的长度", "type": "string_literal"}, {"context": "override val name: String = \"致命属性\"", "line": 38, "text": "致命属性", "type": "string_literal"}, {"context": "override val desc: String = \"接触后小蛇死亡，游戏结束\"", "line": 39, "text": "接触后小蛇死亡，游戏结束", "type": "string_literal"}, {"context": "override val name: String = \"支撑点属性\"", "line": 45, "text": "支撑点属性", "type": "string_literal"}, {"context": "override val desc: String = \"可以支撑有重力属性的元素不下落\"", "line": 46, "text": "可以支撑有重力属性的元素不下落", "type": "string_literal"}, {"context": "override val name: String = \"可推动属性\"", "line": 52, "text": "可推动属性", "type": "string_literal"}, {"context": "override val desc: String = \"可以被其他可移动元素推动\"", "line": 53, "text": "可以被其他可移动元素推动", "type": "string_literal"}, {"context": "override val name: String = \"胜利属性\"", "line": 59, "text": "胜利属性", "type": "string_literal"}, {"context": "override val desc: String = \"小蛇头部接触后通关\"", "line": 60, "text": "小蛇头部接触后通关", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/game/core/Elements.kt": [{"context": "name = \"空格\",", "line": 39, "text": "空格", "type": "string_literal"}, {"context": "desc = \"空的地图格子，可以被其他元素占据\",", "line": 40, "text": "空的地图格子，可以被其他元素占据", "type": "string_literal"}, {"context": "name = \"墙壁\",", "line": 48, "text": "墙壁", "type": "string_literal"}, {"context": "desc = \"固定的障碍物，提供支撑\",", "line": 49, "text": "固定的障碍物，提供支撑", "type": "string_literal"}, {"context": "name = \"苹果\",", "line": 57, "text": "苹果", "type": "string_literal"}, {"context": "desc = \"普通食物，小蛇吃掉后长度+1\",", "line": 58, "text": "普通食物，小蛇吃掉后长度+1", "type": "string_literal"}, {"context": "name = \"毒刺\",", "line": 66, "text": "毒刺", "type": "string_literal"}, {"context": "desc = \"危险的尖刺，接触后小蛇死亡\",", "line": 67, "text": "危险的尖刺，接触后小蛇死亡", "type": "string_literal"}, {"context": "name = \"石头\",", "line": 75, "text": "石头", "type": "string_literal"}, {"context": "desc = \"可推动的石头，有重力会下落\",", "line": 76, "text": "可推动的石头，有重力会下落", "type": "string_literal"}, {"context": "name = \"出口\",", "line": 84, "text": "出口", "type": "string_literal"}, {"context": "desc = \"关卡出口，小蛇到达后通关\",", "line": 85, "text": "关卡出口，小蛇到达后通关", "type": "string_literal"}, {"context": "override val name: String = \"蛇身\",", "line": 102, "text": "蛇身", "type": "string_literal"}, {"context": "override val desc: String = \"小蛇的身体部分\",", "line": 103, "text": "小蛇的身体部分", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/game/core/LevelMap.kt": [{"context": "throw IllegalArgumentException(\"请使用placeSnakeHead或placeSnakeBody方法添加小蛇\")", "line": 64, "text": "请使用placeSnakeHead或placeSnakeBody方法添加小蛇", "type": "string_literal"}, {"context": "throw IllegalStateException(\"只能从蛇尾开始移除\")", "line": 86, "text": "只能从蛇尾开始移除", "type": "string_literal"}, {"context": "throw IllegalStateException(\"只能设置一个蛇头\")", "line": 105, "text": "只能设置一个蛇头", "type": "string_literal"}, {"context": "throw IllegalStateException(\"请先设置蛇头\")", "line": 119, "text": "请先设置蛇头", "type": "string_literal"}, {"context": "throw IllegalArgumentException(\"地图尺寸不能小于1\")", "line": 136, "text": "地图尺寸不能小于1", "type": "string_literal"}, {"context": "throw IllegalArgumentException(\"起始位置不能大于结束位置\")", "line": 206, "text": "起始位置不能大于结束位置", "type": "string_literal"}, {"context": "throw IndexOutOfBoundsException(\"平移后区域超出地图边界\")", "line": 220, "text": "平移后区域超出地图边界", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/game/core/Snake.kt": [{"context": "\"蛇太短了，至少要有一节身体\"", "line": 43, "text": "蛇太短了，至少要有一节身体", "type": "string_literal"}, {"context": "check(uniquePositions.size == segments.size) { \"蛇的身体出现重叠\" }", "line": 50, "text": "蛇的身体出现重叠", "type": "string_literal"}, {"context": "check(isConnected) { \"蛇的身体断开了，每一节都要连在一起\" }", "line": 59, "text": "蛇的身体断开了，每一节都要连在一起", "type": "string_literal"}, {"context": "check(validHead) { \"蛇头的位置不对，找不到一条可以走完全身的路径\" }", "line": 66, "text": "蛇头的位置不对，找不到一条可以走完全身的路径", "type": "string_literal"}, {"context": "check(!invalidDirection) { \"蛇头正对着自己的身体，这样会撞到自己\" }", "line": 80, "text": "蛇头正对着自己的身体，这样会撞到自己", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im/UCInstanceMessage.kt": [{"context": "return \"(已撤回)${getSummaryString()}\"", "line": 29, "text": "(已撤回)${getSummaryString()}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im/api/IMApi.kt": [{"context": "*   \"toast\": \"陌生人之间最多只能发送15条消息哦\",  # toast 提示，为空字符串则不 toast", "line": 20, "text": "陌生人之间最多只能发送15条消息哦", "type": "string_literal"}, {"context": "*   \"hint\": \"陌生人之间最多发送15条消息\\n互相关注或者添加对方为好友后聊天不限制\",  # trigger_block_card 为 True 时为拦截卡片文案", "line": 22, "text": "陌生人之间最多发送15条消息\\n互相关注或者添加对方为好友后聊天不限制", "type": "string_literal"}, {"context": "*   \"block_card_btn_txt\": \"添加对方为好友\"  # trigger_block_card 为 True 时为拦截卡片按钮文案", "line": 23, "text": "添加对方为好友", "type": "string_literal"}, {"context": "*   \"hint\": \"您的免费消息条数已用完，接下来发消息{}钻石/条\\n和对方互相关注成为好友后，可无限制聊天\"", "line": 28, "text": "您的免费消息条数已用完，接下来发消息{}钻石/条\\n和对方互相关注成为好友后，可无限制聊天", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/GlobalNotification.kt": [{"context": "name = \"哈哈哈大地方\",", "line": 482, "text": "哈哈哈大地方", "type": "string_literal"}, {"context": "desc = buildAnnotatedString { append(\"哈哈哈大地方\") },", "line": 485, "text": "哈哈哈大地方", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/conversation/AppConversationManger.kt": [{"context": "IMLogUtils.i(\"开始加载会话列表\")", "line": 132, "text": "开始加载会话列表", "type": "string_literal"}, {"context": "LogUtils.d(\"IMCompatCore\", \"未知类型的会话: ${it.id}\")", "line": 382, "text": "未知类型的会话: ${it.id}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/types/UCCustomMessage.kt": [{"context": "override fun getSummaryString(): String = \"自定义消息, cmd: $cmd, summary: $summary\"", "line": 68, "text": "自定义消息, cmd: $cmd, summary: $summary", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/types/UCEmojiMessage.kt": [{"context": "TYPE_DICE -> \"骰子\"", "line": 26, "text": "骰子", "type": "string_literal"}, {"context": "in TYPE_NUMBER_1..TYPE_NUMBER_5 -> \"幸运数字\"", "line": 27, "text": "幸运数字", "type": "string_literal"}, {"context": "TYPE_GUESSING_FIST -> \"猜拳\"", "line": 28, "text": "猜拳", "type": "string_literal"}, {"context": "else -> \"未知\"", "line": 29, "text": "未知", "type": "string_literal"}, {"context": "return \"表情消息(${name}), type: $type, name: $value\"", "line": 31, "text": "表情消息(${name}), type: $type, name: $value", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/types/UCFakeMessage.kt": [{"context": "override fun getSummaryString(): String = \"时间戳：${getMessageTimeFormatText(WakooApplication.instance)}\"", "line": 33, "text": "时间戳：${getMessageTimeFormatText(WakooApplication.instance)}", "type": "string_literal"}, {"context": "override fun getSummaryString(): String = \"以下为新消息\"", "line": 63, "text": "以下为新消息", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/types/UCGiftMessage.kt": [{"context": "override fun getSummaryString(): String = \"礼物消息, id: ${gift.gift.id}, 名称: ${gift.gift.name}\"", "line": 19, "text": "礼物消息, id: ${gift.gift.id}, 名称: ${gift.gift.name}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/types/UCImageMessage.kt": [{"context": "return \"图片消息, 宽: ${elem?.width}, 高: ${elem?.height}, url: ${elem?.source}\"", "line": 33, "text": "图片消息, 宽: ${elem?.width}, 高: ${elem?.height}, url: ${elem?.source}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/types/UCMicEmojiMessage.kt": [{"context": "TYPE_WEBP -> \"普通动态表情\"", "line": 31, "text": "普通动态表情", "type": "string_literal"}, {"context": "TYPE_WEBP_AND_RESULT -> \"带结果的动态表情\"", "line": 32, "text": "带结果的动态表情", "type": "string_literal"}, {"context": "return \"表情消息(${content.emojiEffect.name}), type: $type\"", "line": 35, "text": "表情消息(${content.emojiEffect.name}), type: $type", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/types/UCTextMessage.kt": [{"context": "override fun getSummaryString(): String = \"文本消息：$text\"", "line": 21, "text": "文本消息：$text", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/types/UCUnknownMessage.kt": [{"context": "return \"未知消息类型：${base}\"", "line": 12, "text": "未知消息类型：${base}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/types/UCVoiceMessage.kt": [{"context": "override fun getSummaryString(): String = \"语音消息, 时长: $duration, url: $url\"", "line": 16, "text": "语音消息, 时长: $duration, url: $url", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/ui/custom/CommonTextButtonContent.kt": [{"context": "\"哈哈哈哈哈\",", "line": 72, "text": "哈哈哈哈哈", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/ui/custom/MsgInterceptContent.kt": [{"context": "\"这是测试用的消息拦截消息展示\",", "line": 125, "text": "这是测试用的消息拦截消息展示", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/ui/custom/SendGiftMessageItem.kt": [{"context": "val g = GiftWall.GiftWrapper.Gift(name = \"礼物名称\", price = 5200)", "line": 138, "text": "礼物名称", "type": "string_literal"}, {"context": "message = \"你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦\",", "line": 141, "text": "你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦", "type": "string_literal"}, {"context": "buttonText = \"免费赠送\",", "line": 142, "text": "免费赠送", "type": "string_literal"}, {"context": "message = \"你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦\",", "line": 147, "text": "你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦", "type": "string_literal"}, {"context": "buttonText = \"已赠送\",", "line": 148, "text": "已赠送", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/ui/entry/GiftMsgEntry.kt": [{"context": "\"赠送福袋获得奖励\",", "line": 167, "text": "赠送福袋获得奖励", "type": "string_literal"}, {"context": "\"赠送多人福袋获得奖励\",", "line": 177, "text": "赠送多人福袋获得奖励", "type": "string_literal"}, {"context": "\"赠送福袋没有获得奖励\",", "line": 190, "text": "赠送福袋没有获得奖励", "type": "string_literal"}, {"context": "\"赠送多人福袋没有获得奖励\",", "line": 199, "text": "赠送多人福袋没有获得奖励", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/ui/entry/PrivateRoomEventEntry.kt": [{"context": "\"幼儿园搬花已经进入了私密小屋\",", "line": 105, "text": "幼儿园搬花已经进入了私密小屋", "type": "string_literal"}, {"context": "\"加入互动\",", "line": 106, "text": "加入互动", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/message/ui/entry/VoiceCallEventEntry.kt": [{"context": "\"对方挂断了语音,本次通话已结束点击下方按钮可继续聊天\",", "line": 79, "text": "对方挂断了语音,本次通话已结束点击下方按钮可继续聊天", "type": "string_literal"}, {"context": "\"继续聊天\",", "line": 80, "text": "继续聊天", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/rtm/RtmMsgHandler.kt": [{"context": "\"飘屏赠送福袋获得奖励\",", "line": 256, "text": "飘屏赠送福袋获得奖励", "type": "string_literal"}, {"context": "\"飘屏赠送多人福袋获得奖励\",", "line": 267, "text": "飘屏赠送多人福袋获得奖励", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/im_business/tim/TIMEngine.kt": [{"context": "IMLogUtils.i(\"重发消息成功 => $rawMessage\")", "line": 1172, "text": "重发消息成功 => $rawMessage", "type": "string_literal"}, {"context": "IMLogUtils.i(\"发送消息成功 => $rawMessage\")", "line": 1174, "text": "发送消息成功 => $rawMessage", "type": "string_literal"}, {"context": "IMLogUtils.i(\"重发消息失败code$code => desc:$desc => $rawMessage\")", "line": 1202, "text": "重发消息失败code$code => desc:$desc => $rawMessage", "type": "string_literal"}, {"context": "IMLogUtils.i(\"发送消息失败code$code => desc:$desc => $rawMessage\")", "line": 1204, "text": "发送消息失败code$code => desc:$desc => $rawMessage", "type": "string_literal"}, {"context": "IMLogUtils.w(\"当前的user = null, 不能转换成IMUser\")", "line": 1261, "text": "当前的user = null, 不能转换成IMUser", "type": "string_literal"}, {"context": "IMLogUtils.i(\"重发消息通知:$ucMessage\")", "line": 1344, "text": "重发消息通知:$ucMessage", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/manager/AudioPlayerManager.kt": [{"context": "LogUtils.d(\"AudioPlayerManager.play() 开始播放音频, tag=$tag, uri=$uri\")", "line": 165, "text": "AudioPlayerManager.play() 开始播放音频, tag=$tag, uri=$uri", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.onPrepared() 音频准备完成, tag=$tag, duration=${audioDuration}ms\")", "line": 179, "text": "AudioPlayerManager.onPrepared() 音频准备完成, tag=$tag, duration=${audioDuration}ms", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.onPrepared() 开始播放并更新状态, tag=$tag\")", "line": 195, "text": "AudioPlayerManager.onPrepared() 开始播放并更新状态, tag=$tag", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.onCompletion() 播放完成, tag=$tag\")", "line": 200, "text": "AudioPlayerManager.onCompletion() 播放完成, tag=$tag", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.onSeekComplete() 跳转完成, tag=$tag, position=${accumulatedTimeMs}ms\")", "line": 208, "text": "AudioPlayerManager.onSeekComplete() 跳转完成, tag=$tag, position=${accumulatedTimeMs}ms", "type": "string_literal"}, {"context": "LogUtils.e(\"AudioPlayerManager.onError() 播放出错, tag=$tag, what=$what, extra=$extra\")", "line": 217, "text": "AudioPlayerManager.onError() 播放出错, tag=$tag, what=$what, extra=$extra", "type": "string_literal"}, {"context": "LogUtils.e(e, \"AudioPlayerManager.play() 播放初始化失败, tag=$tag\")", "line": 233, "text": "AudioPlayerManager.play() 播放初始化失败, tag=$tag", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.pause() 暂停播放, tag=${currentState.currentlyPlayingTag}, isPlaying=${mediaPlayer?.isPlaying}\")", "line": 249, "text": "AudioPlayerManager.pause() 暂停播放, tag=${currentState.currentlyPlayingTag}, isPlaying=${mediaPlayer?.isPlaying}", "type": "string_literal"}, {"context": "\"AudioPlayerManager.pause() 累加播放时长, tag=${currentState.currentlyPlayingTag}, segmentDuration=${segmentDuration}ms, totalAccumulated=${accumulatedTimeMs}ms\",", "line": 258, "text": "AudioPlayerManager.pause() 累加播放时长, tag=${currentState.currentlyPlayingTag}, segmentDuration=${segmentDuration}ms, totalAccumulated=${accumulatedTimeMs}ms", "type": "string_literal"}, {"context": "\"AudioPlayerManager.pause() 暂停完成, tag=${currentState.currentlyPlayingTag}, position=${accumulatedTimeMs.toInt()}ms\",", "line": 273, "text": "AudioPlayerManager.pause() 暂停完成, tag=${currentState.currentlyPlayingTag}, position=${accumulatedTimeMs.toInt()}ms", "type": "string_literal"}, {"context": "\"AudioPlayerManager.resume() 恢复播放, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}\",", "line": 285, "text": "AudioPlayerManager.resume() 恢复播放, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.resume() 重置播放起始时间, tag=${currentState.currentlyPlayingTag}, startTime=$playbackStartTimeMs\")", "line": 293, "text": "AudioPlayerManager.resume() 重置播放起始时间, tag=${currentState.currentlyPlayingTag}, startTime=$playbackStartTimeMs", "type": "string_literal"}, {"context": "\"AudioPlayerManager.resume() 恢复播放完成, tag=${currentState.currentlyPlayingTag}, position=${currentState.currentPosition}ms\",", "line": 305, "text": "AudioPlayerManager.resume() 恢复播放完成, tag=${currentState.currentlyPlayingTag}, position=${currentState.currentPosition}ms", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.release() 跳过释放，tag不匹配, 指定tag=$tag, 当前tag=$currentlyPlayingTag\")", "line": 317, "text": "AudioPlayerManager.release() 跳过释放，tag不匹配, 指定tag=$tag, 当前tag=$currentlyPlayingTag", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.release() 跳过释放，tag不匹配, 当前tag=$currentlyPlayingTag\")", "line": 323, "text": "AudioPlayerManager.release() 跳过释放，tag不匹配, 当前tag=$currentlyPlayingTag", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.release() 开始释放资源, tag=$currentTag, 当前tag=$currentTag\")", "line": 341, "text": "AudioPlayerManager.release() 开始释放资源, tag=$currentTag, 当前tag=$currentTag", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.release() 释放完成, tag=$currentTag\")", "line": 354, "text": "AudioPlayerManager.release() 释放完成, tag=$currentTag", "type": "string_literal"}, {"context": "\"AudioPlayerManager.seekTo() 跳转播放位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms, currentState=${currentState::class.simpleName}\",", "line": 365, "text": "AudioPlayerManager.seekTo() 跳转播放位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms, currentState=${currentState::class.simpleName}", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.seekTo() 更新Playing状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms\")", "line": 374, "text": "AudioPlayerManager.seekTo() 更新Playing状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.seekTo() 更新Paused状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms\")", "line": 379, "text": "AudioPlayerManager.seekTo() 更新Paused状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms", "type": "string_literal"}, {"context": "\"AudioPlayerManager.seekTo() 忽略seekTo，当前状态不支持, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}\",", "line": 384, "text": "AudioPlayerManager.seekTo() 忽略seekTo，当前状态不支持, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.startProgressUpdate() 开始进度更新, tag=${currentState.currentlyPlayingTag}\")", "line": 396, "text": "AudioPlayerManager.startProgressUpdate() 开始进度更新, tag=${currentState.currentlyPlayingTag}", "type": "string_literal"}, {"context": "LogUtils.d(\"AudioPlayerManager.stopProgressUpdate() 停止进度更新, tag=${currentState.currentlyPlayingTag}\")", "line": 426, "text": "AudioPlayerManager.stopProgressUpdate() 停止进度更新, tag=${currentState.currentlyPlayingTag}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/manager/AudioRecordManager.kt": [{"context": "val channelDesc = if (config.audioChannels == 1) \"单声道\" else \"立体声\"", "line": 81, "text": "单声道", "type": "string_literal"}, {"context": "val channelDesc = if (config.audioChannels == 1) \"单声道\" else \"立体声\"", "line": 81, "text": "立体声", "type": "string_literal"}, {"context": "config.audioEncodingBitRate >= 128000 -> \"高音质\"", "line": 84, "text": "高音质", "type": "string_literal"}, {"context": "config.audioEncodingBitRate >= 64000 -> \"标准音质\"", "line": 85, "text": "标准音质", "type": "string_literal"}, {"context": "else -> \"节省空间\"", "line": 86, "text": "节省空间", "type": "string_literal"}, {"context": "LogUtils.w(\"录音已在进行中，无法重复开始\")", "line": 259, "text": "录音已在进行中，无法重复开始", "type": "string_literal"}, {"context": "LogUtils.d(\"检测到暂停状态，自动恢复录音\")", "line": 275, "text": "检测到暂停状态，自动恢复录音", "type": "string_literal"}, {"context": "LogUtils.d(\"录音开始: ${outputFile.absolutePath}\\n配置: $config\")", "line": 319, "text": "录音开始: ${outputFile.absolutePath}\\n配置: $config", "type": "string_literal"}, {"context": "LogUtils.w(\"当前状态无法暂停录制\")", "line": 352, "text": "当前状态无法暂停录制", "type": "string_literal"}, {"context": "LogUtils.d(\"录音暂停\")", "line": 370, "text": "录音暂停", "type": "string_literal"}, {"context": "LogUtils.w(\"当前状态无法恢复录制\")", "line": 390, "text": "当前状态无法恢复录制", "type": "string_literal"}, {"context": "LogUtils.d(\"录音恢复\")", "line": 419, "text": "录音恢复", "type": "string_literal"}, {"context": "LogUtils.w(\"当前没有进行录制\")", "line": 440, "text": "当前没有进行录制", "type": "string_literal"}, {"context": "LogUtils.d(\"录音完成: ${currentOutputFile.absolutePath}, 总时长: ${duration}毫秒, 文件大小: ${fileSize}字节, 结束原因: $reasonText\")", "line": 483, "text": "录音完成: ${currentOutputFile.absolutePath}, 总时长: ${duration}毫秒, 文件大小: ${fileSize}字节, 结束原因: $reasonText", "type": "string_literal"}, {"context": "LogUtils.w(\"当前没有进行录制\")", "line": 512, "text": "当前没有进行录制", "type": "string_literal"}, {"context": "LogUtils.d(\"录音取消，临时文件已删除\")", "line": 529, "text": "录音取消，临时文件已删除", "type": "string_literal"}, {"context": "exception.message?.contains(\"存储空间不足\") == true -> RecordingFailureReason.STORAGE_INSUFFICIENT", "line": 566, "text": "存储空间不足", "type": "string_literal"}, {"context": "\"录音错误: $message, 原因: $failureReason\",", "line": 612, "text": "录音错误: $message, 原因: $failureReason", "type": "string_literal"}, {"context": "LogUtils.w(\"检测到录音器异常，可能是系统中断: ${e.message}\")", "line": 673, "text": "检测到录音器异常，可能是系统中断: ${e.message}", "type": "string_literal"}, {"context": "LogUtils.d(\"达到最大录制时长，自动停止录制\")", "line": 692, "text": "达到最大录制时长，自动停止录制", "type": "string_literal"}, {"context": "\"释放录制器失败\",", "line": 729, "text": "释放录制器失败", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/manager/EnvironmentManager.kt": [{"context": "showToast(\"没有找到合适的环境：$currentEnvironmentKey，已自动调整为：${key}环境\")", "line": 65, "text": "没有找到合适的环境：$currentEnvironmentKey，已自动调整为：${key}环境", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/manager/L10nManager.kt": [{"context": "LogUtils.dTag(\"L10nManager\", \"加载国际化包成功: $tag\")", "line": 131, "text": "加载国际化包成功: $tag", "type": "string_literal"}, {"context": "LogUtils.eTag(\"L10nManager\", \"加载国际化包失败: $tag\")", "line": 134, "text": "加载国际化包失败: $tag", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/navigation/AppNavController.kt": [{"context": "error(\"不支持，请使用pushTopLevel\")", "line": 319, "text": "不支持，请使用pushTopLevel", "type": "string_literal"}, {"context": "error(\"不支持，请使用pushTopLevel\")", "line": 325, "text": "不支持，请使用pushTopLevel", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/network/ApiClient.kt": [{"context": "LogUtils.d(\"ApiClient 正在尝试使用 RefreshToken: $refreshTokenValue 进行刷新...\")", "line": 122, "text": "ApiClient 正在尝试使用 RefreshToken: $refreshTokenValue 进行刷新...", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/network/ApiHandler.kt": [{"context": "LogUtils.eTag(\"ApiClient\", \"数据解析异常：$e\", showCallerInfo = false, showThreadInfo = false)", "line": 118, "text": "数据解析异常：$e", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/network/AuthenticationInterceptor.kt": [{"context": "LogUtils.d(\"TokenRefreshInterceptor: 检测到Token过期, 准备刷新. Request: ${originalRequest.url}\")", "line": 108, "text": "TokenRefreshInterceptor: 检测到Token过期, 准备刷新. Request: ${originalRequest.url}", "type": "string_literal"}, {"context": "LogUtils.d(\"TokenRefreshInterceptor: Token已被刷新, 直接使用新Token重试. Request: ${originalRequest.url}\")", "line": 143, "text": "TokenRefreshInterceptor: Token已被刷新, 直接使用新Token重试. Request: ${originalRequest.url}", "type": "string_literal"}, {"context": "LogUtils.d(\"TokenRefreshInterceptor: 开始执行Token刷新操作.\")", "line": 150, "text": "TokenRefreshInterceptor: 开始执行Token刷新操作.", "type": "string_literal"}, {"context": "LogUtils.e(\"TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.\")", "line": 167, "text": "TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.", "type": "string_literal"}, {"context": "LogUtils.w(\"TokenRefreshInterceptor: RefreshToken已过期, 执行登出.\")", "line": 179, "text": "TokenRefreshInterceptor: RefreshToken已过期, 执行登出.", "type": "string_literal"}, {"context": "LogUtils.e(\"TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.\")", "line": 187, "text": "TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.", "type": "string_literal"}, {"context": "LogUtils.w(\"TokenRefreshInterceptor: 首次Token刷新失败, 准备重试一次. Error: ${refreshResult.exceptionOrNull()?.message}\")", "line": 193, "text": "TokenRefreshInterceptor: 首次Token刷新失败, 准备重试一次. Error: ${refreshResult.exceptionOrNull()?.message}", "type": "string_literal"}, {"context": "LogUtils.d(\"TokenRefreshInterceptor: Token刷新成功!\")", "line": 203, "text": "TokenRefreshInterceptor: Token刷新成功!", "type": "string_literal"}, {"context": "LogUtils.e(\"TokenRefreshInterceptor: Token刷新彻底失败. Error: ${refreshResult.exceptionOrNull()?.message}\")", "line": 207, "text": "TokenRefreshInterceptor: Token刷新彻底失败. Error: ${refreshResult.exceptionOrNull()?.message}", "type": "string_literal"}, {"context": "LogUtils.d(\"TokenRefreshInterceptor: 使用新Token继续请求. New Token: $accessToken, Request: ${request.url}\")", "line": 227, "text": "TokenRefreshInterceptor: 使用新Token继续请求. New Token: $accessToken, Request: ${request.url}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/network/ErrorHandlingInterceptor.kt": [{"context": "LogUtils.e(\"ErrorHandlingInterceptor: 网络连接异常 - ${e.javaClass.simpleName}: ${e.message}\")", "line": 141, "text": "ErrorHandlingInterceptor: 网络连接异常 - ${e.javaClass.simpleName}: ${e.message}", "type": "string_literal"}, {"context": "LogUtils.e(\"ErrorHandlingInterceptor: 通用IO异常 - ${e.javaClass.simpleName}: ${e.message} $request\")", "line": 148, "text": "ErrorHandlingInterceptor: 通用IO异常 - ${e.javaClass.simpleName}: ${e.message} $request", "type": "string_literal"}, {"context": "LogUtils.e(\"ErrorHandlingInterceptor: 未知错误 - ${e.javaClass.simpleName}: ${e.message}\")", "line": 161, "text": "ErrorHandlingInterceptor: 未知错误 - ${e.javaClass.simpleName}: ${e.message}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/network/api/bean/LoginApiBean.kt": [{"context": "* (301, \"WAKOO_PHONE\", \"Wakoo手机号登录\")", "line": 30, "text": "Wakoo手机号登录", "type": "string_literal"}, {"context": "* (302, \"WAKOO_GOOGLE\", \"Wakoo Google登录\")", "line": 31, "text": "Wakoo Google登录", "type": "string_literal"}, {"context": "* (307, \"WAKOO_FACEBOOK2\", \"Wakoo FB登录\")", "line": 32, "text": "Wakoo FB登录", "type": "string_literal"}, {"context": "* (308, \"WAKOO_LINE\", \"Wakoo Line登录\")", "line": 33, "text": "Wakoo Line登录", "type": "string_literal"}, {"context": "* (305, \"WAKOO_FAST_LOGIN_ANDROID\", \"Wakoo Android快速登录\")", "line": 34, "text": "Wakoo Android快速登录", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/network/api/bean/UserApiBean.kt": [{"context": "\"region_label\": \"中国大陆\",", "line": 34, "text": "中国大陆", "type": "string_literal"}, {"context": "\"region_reason_label\": \"现居地\",", "line": 36, "text": "现居地", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/network/api/bean/VoiceFeedResponse.kt": [{"context": "* \"id\": 1, \"name\": \"测试多语言问题\" }, { \"id\": 2, \"name\": \"美好的日子\" }, { \"id\": 5, \"name\": \"青春\" } ],", "line": 54, "text": "测试多语言问题", "type": "string_literal"}, {"context": "* \"id\": 1, \"name\": \"测试多语言问题\" }, { \"id\": 2, \"name\": \"美好的日子\" }, { \"id\": 5, \"name\": \"青春\" } ],", "line": 54, "text": "美好的日子", "type": "string_literal"}, {"context": "* \"id\": 1, \"name\": \"测试多语言问题\" }, { \"id\": 2, \"name\": \"美好的日子\" }, { \"id\": 5, \"name\": \"青春\" } ],", "line": 54, "text": "青春", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/network/api/bean/WalletBean.kt": [{"context": "*     (2, \"APPLE_SDK\", \"苹果SDK\"),", "line": 32, "text": "苹果SDK", "type": "string_literal"}, {"context": "*     (3, \"WXPAY_SDK\", \"微信SDK\"),", "line": 33, "text": "微信SDK", "type": "string_literal"}, {"context": "*     (4, \"ORDER_LINK\", \"获取订单链接后打开\"),", "line": 34, "text": "获取订单链接后打开", "type": "string_literal"}, {"context": "*     (6, \"LINK_WEB\", \"浏览器打开链接\"),", "line": 36, "text": "浏览器打开链接", "type": "string_literal"}, {"context": "*     (7, \"AGENT\", \"代充\"),", "line": 37, "text": "代充", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/network/api/service/GiftApiService.kt": [{"context": "*      *    (0, \"UNKNOWN\", \"未知\"),", "line": 25, "text": "未知", "type": "string_literal"}, {"context": "*      *     (1, \"TRIBE\", \"部落\"),", "line": 26, "text": "部落", "type": "string_literal"}, {"context": "*      *     (2, \"AUDI<PERSON><PERSON><PERSON>\", \"语音房\"),", "line": 27, "text": "语音房", "type": "string_literal"}, {"context": "*      *     (3, \"RPIVATERO<PERSON>\", \"私密小屋\"),", "line": 28, "text": "私密小屋", "type": "string_literal"}, {"context": "*      *     (4, \"RPIVATE_CHAT\", \"私聊\"),", "line": 29, "text": "私聊", "type": "string_literal"}, {"context": "*      *     (5, \"MOMENT\", \"动态\"),", "line": 30, "text": "动态", "type": "string_literal"}, {"context": "*      *     (6, \"PROFILE\", \"个人主页\"),", "line": 31, "text": "个人主页", "type": "string_literal"}, {"context": "*      *     (7, \"CHATGROUP\", \"群聊\"),", "line": 32, "text": "群聊", "type": "string_literal"}, {"context": "*      *     (8, \"AUDIO_CHAT_USER_RECOM\", \"语音聊天用户推荐\"),", "line": 33, "text": "语音聊天用户推荐", "type": "string_literal"}, {"context": "*      *     (9, \"CP_ROOM\", \"CP小屋\"),", "line": 34, "text": "CP小屋", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/repository/AccountPreferencesRepository.kt": [{"context": "LogUtils.d(\"没有找到登录信息\")", "line": 62, "text": "没有找到登录信息", "type": "string_literal"}, {"context": "LogUtils.d(\"从JSON读取到用户信息: id=${it.id}, name=${it.name}\")", "line": 71, "text": "从JSON读取到用户信息: id=${it.id}, name=${it.name}", "type": "string_literal"}, {"context": "LogUtils.e(e, \"读取用户信息失败\")", "line": 77, "text": "读取用户信息失败", "type": "string_literal"}, {"context": "LogUtils.d(\"保存用户信息成功: id=${accountInfo.id}, name=${accountInfo.name}\")", "line": 106, "text": "保存用户信息成功: id=${accountInfo.id}, name=${accountInfo.name}", "type": "string_literal"}, {"context": "LogUtils.e(e, \"保存用户信息失败: ${e.message}\")", "line": 111, "text": "保存用户信息失败: ${e.message}", "type": "string_literal"}, {"context": "LogUtils.d(\"清除用户信息成功\")", "line": 122, "text": "清除用户信息成功", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/dialog/CommonDialogs.kt": [{"context": "title = \"注销账号\",", "line": 495, "text": "注销账号", "type": "string_literal"}, {"context": "content = \"注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。\",", "line": 496, "text": "注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。", "type": "string_literal"}, {"context": "cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = \"取消\"),", "line": 497, "text": "取消", "type": "string_literal"}, {"context": "confirmButtonConfig = DialogButtonStyles.Primary.copy(text = \"确认注销\"),", "line": 498, "text": "确认注销", "type": "string_literal"}, {"context": "title = \"注销账号\",", "line": 521, "text": "注销账号", "type": "string_literal"}, {"context": "content = \"注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。\",", "line": 522, "text": "注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。", "type": "string_literal"}, {"context": "confirmButtonConfig = DialogButtonStyles.Primary.copy(text = \"取消\"),", "line": 523, "text": "取消", "type": "string_literal"}, {"context": "cancelButtonConfig = DialogButtonStyles.Transparent.copy(text = \"确认注销\"),", "line": 524, "text": "确认注销", "type": "string_literal"}, {"context": "content = \"本次选择的卡片主题需要花费200钻石，您的钻石余额不足，请充值\",", "line": 547, "text": "本次选择的卡片主题需要花费200钻石，您的钻石余额不足，请充值", "type": "string_literal"}, {"context": "buttonConfig = DialogButtonStyles.Primary.copy(text = \"充值钻石\"),", "line": 548, "text": "充值钻石", "type": "string_literal"}, {"context": "content = \"本次选择的卡片主题仅会员用户可以使用，快去开通会员吧！\",", "line": 554, "text": "本次选择的卡片主题仅会员用户可以使用，快去开通会员吧！", "type": "string_literal"}, {"context": "buttonConfig = DialogButtonStyles.VIP.copy(text = \"开通会员\"),", "line": 555, "text": "开通会员", "type": "string_literal"}, {"context": "content = \"本次选择的卡片主题需要花费200钻石，确认扣除钻石并发布作品吗？\",", "line": 651, "text": "本次选择的卡片主题需要花费200钻石，确认扣除钻石并发布作品吗？", "type": "string_literal"}, {"context": "cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = \"取消\"),", "line": 652, "text": "取消", "type": "string_literal"}, {"context": "confirmButtonConfig = DialogButtonStyles.Primary.copy(text = \"确认\"),", "line": 653, "text": "确认", "type": "string_literal"}, {"context": "title = \"内容合规准则\",", "line": 673, "text": "内容合规准则", "type": "string_literal"}, {"context": "content = \"我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。\\n\\n我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。\",", "line": 674, "text": "我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。\\n\\n我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/dialog/FilterUserAreaWidget.kt": [{"context": "text = \"开始筛选\",", "line": 128, "text": "开始筛选", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/dialog/FirstClassNotifyDialog.kt": [{"context": "headMessage = \"恭喜您\",", "line": 289, "text": "恭喜您", "type": "string_literal"}, {"context": "middleMessage = \"成為尊敬的頭等艙俱樂部成員\",", "line": 290, "text": "成為尊敬的頭等艙俱樂部成員", "type": "string_literal"}, {"context": "message = \"所有俱樂部成員特權已解鎖,祝您使用愉快\",", "line": 292, "text": "所有俱樂部成員特權已解鎖,祝您使用愉快", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/dialog/PrivateRoomNotificationDialog.kt": [{"context": "content = \"您的免费时长已不足，\\n可添加好友享有无限通话时长。\",", "line": 209, "text": "您的免费时长已不足，\\n可添加好友享有无限通话时长。", "type": "string_literal"}, {"context": "button = \"加好友\",", "line": 210, "text": "加好友", "type": "string_literal"}, {"context": "append(\"丨\")", "line": 292, "text": "丨", "type": "string_literal"}, {"context": "hint = \"她正在找人语音连麦聊天...\",", "line": 407, "text": "她正在找人语音连麦聊天...", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/dialog/TaskRewardDialog.kt": [{"context": "title = \"这个是标题\",", "line": 280, "text": "这个是标题", "type": "string_literal"}, {"context": "desc = \"这是测试的内容这是测试的内容这是测试的内容这是测试的内容这是测试的内容\",", "line": 288, "text": "这是测试的内容这是测试的内容这是测试的内容这是测试的内容这是测试的内容", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/dialog/UserTaskFinishedDialog.kt": [{"context": "\"恭喜聊天任务完成\", \"恭喜获得了xxxx积分\", \"领取现金\"", "line": 76, "text": "恭喜聊天任务完成", "type": "string_literal"}, {"context": "\"恭喜聊天任务完成\", \"恭喜获得了xxxx积分\", \"领取现金\"", "line": 76, "text": "恭喜获得了xxxx积分", "type": "string_literal"}, {"context": "\"恭喜聊天任务完成\", \"恭喜获得了xxxx积分\", \"领取现金\"", "line": 76, "text": "领取现金", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/floating/BuddyFloating.kt": [{"context": "\"哈哈哈\",", "line": 292, "text": "哈哈哈", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/ChatGroupEntryCard.kt": [{"context": "ChatGroupEntryCard(ChatGroupBean(name = \"星空闪耀\", memberCnt = 122))", "line": 80, "text": "星空闪耀", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/chat/ChatGroupMessageUI.kt": [{"context": "append(\"恭喜新人\")", "line": 270, "text": "恭喜新人", "type": "string_literal"}, {"context": "append(\"加入群组\")", "line": 274, "text": "加入群组", "type": "string_literal"}, {"context": "append(\"送给\")", "line": 379, "text": "送给", "type": "string_literal"}, {"context": "messageGift = buildAnnotatedString { append(\"梦幻城包x1\") },", "line": 385, "text": "梦幻城包x1", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/member/GroupMemberList.kt": [{"context": "\"周活跃度\", modifier = Modifier", "line": 86, "text": "周活跃度", "type": "string_literal"}, {"context": "\"总活跃度\", modifier = Modifier", "line": 104, "text": "总活跃度", "type": "string_literal"}, {"context": "TextSort(\"周活跃度\")", "line": 159, "text": "周活跃度", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/panel/EditGroupInfoPanel.kt": [{"context": "title = \"修改群组名称\",", "line": 95, "text": "修改群组名称", "type": "string_literal"}, {"context": "placeholder = \"请输入家族昵称\",", "line": 97, "text": "请输入家族昵称", "type": "string_literal"}, {"context": "title = \"修改群组简介\",", "line": 112, "text": "修改群组简介", "type": "string_literal"}, {"context": "placeholder = \"请输入家族昵称\",", "line": 114, "text": "请输入家族昵称", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/screen/ChatGroupDescriptionScreen.kt": [{"context": "name = \"星空闪耀\",", "line": 604, "text": "星空闪耀", "type": "string_literal"}, {"context": "\"        \\\"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\\\" +\\n\" +", "line": 608, "text": "在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\\", "type": "string_literal"}, {"context": "\"            \\\"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\\\" +\\n\" +", "line": 609, "text": "在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\\", "type": "string_literal"}, {"context": "\"            \\\"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\\\"\",", "line": 610, "text": "在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\\", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/screen/ChatGroupSettings.kt": [{"context": "name = \"群聊名称哈哈哈\",", "line": 508, "text": "群聊名称哈哈哈", "type": "string_literal"}, {"context": "bulletin = \"介绍介绍介绍介绍介绍介绍介...\",", "line": 509, "text": "介绍介绍介绍介绍介绍介绍介...", "type": "string_literal"}, {"context": "name = \"群聊名称哈哈哈\",", "line": 523, "text": "群聊名称哈哈哈", "type": "string_literal"}, {"context": "bulletin = \"介绍介绍介绍介绍介绍介绍介...\",", "line": 525, "text": "介绍介绍介绍介绍介绍介绍介...", "type": "string_literal"}, {"context": "val group = ChatGroupBean(\"123456\", name = \"群聊名称哈哈哈\", bulletin = \"介绍介绍介绍介绍介绍介绍介...\")", "line": 535, "text": "群聊名称哈哈哈", "type": "string_literal"}, {"context": "val group = ChatGroupBean(\"123456\", name = \"群聊名称哈哈哈\", bulletin = \"介绍介绍介绍介绍介绍介绍介...\")", "line": 535, "text": "介绍介绍介绍介绍介绍介绍介...", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/screen/ChatGroupSquareScreen.kt": [{"context": "text = \"${item.memberCnt}人\",", "line": 360, "text": "${item.memberCnt}人", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/tasks/models/ActiveTaskInfo.kt": [{"context": "*     CHECK_IN = 1, \"签到\"", "line": 12, "text": "签到", "type": "string_literal"}, {"context": "*     GIVE_GIFT = 2, \"送礼\"", "line": 13, "text": "送礼", "type": "string_literal"}, {"context": "*     SEND_MESSAGE = 3, \"发言\"", "line": 14, "text": "发言", "type": "string_literal"}, {"context": "*     FIRST_GIVE_GIFT = 4, \"首次送礼\"", "line": 15, "text": "首次送礼", "type": "string_literal"}, {"context": "*     SEND_MESSAGE_WITH_CP = 5, \"与 CP一起发言\"", "line": 16, "text": "与 CP一起发言", "type": "string_literal"}, {"context": "*     FIRST_MAKE_CP = 1, \"首次组CP\"", "line": 19, "text": "首次组CP", "type": "string_literal"}, {"context": "*     FIRST_PUBLIC_CP = 2, \"首次官宣CP\"", "line": 20, "text": "首次官宣CP", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/tasks/models/TribeActiveInfo.kt": [{"context": "*     \"bonus_desc\": \"奖励描述\"", "line": 17, "text": "奖励描述", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/tasks/ui/GroupTaskUI.kt": [{"context": "add(BonusLevel(1, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 739, "text": "勋章*8天", "type": "string_literal"}, {"context": "add(BonusLevel(1, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 739, "text": "名称", "type": "string_literal"}, {"context": "add(BonusLevel(1, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 739, "text": "勋章*8天", "type": "string_literal"}, {"context": "add(BonusLevel(1, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 739, "text": "名称", "type": "string_literal"}, {"context": "add(BonusLevel(2, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 740, "text": "勋章*8天", "type": "string_literal"}, {"context": "add(BonusLevel(2, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 740, "text": "名称", "type": "string_literal"}, {"context": "add(BonusLevel(2, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 740, "text": "勋章*8天", "type": "string_literal"}, {"context": "add(BonusLevel(2, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 740, "text": "名称", "type": "string_literal"}, {"context": "add(BonusLevel(4, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 741, "text": "勋章*8天", "type": "string_literal"}, {"context": "add(BonusLevel(4, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 741, "text": "名称", "type": "string_literal"}, {"context": "add(BonusLevel(4, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 741, "text": "勋章*8天", "type": "string_literal"}, {"context": "add(BonusLevel(4, listOf(Bonus(\"勋章*8天\", \"\", \"名称\"), Bonus(\"勋章*8天\", \"\", \"名称\"))))", "line": 741, "text": "名称", "type": "string_literal"}, {"context": "ActiveReward(title = \"恭喜获得\", icon = \"\", desc = \"活跃度+10\", buttonText = \"开心收下\")", "line": 744, "text": "活跃度+10", "type": "string_literal"}, {"context": "taskName = \"群组发言\",", "line": 839, "text": "群组发言", "type": "string_literal"}, {"context": "add(ActiveTask(taskName = \"群组签到\", taskBtnLabel = \"签到\"))", "line": 843, "text": "群组签到", "type": "string_literal"}, {"context": "add(ActiveTask(taskName = \"群组签到\", taskBtnLabel = \"签到\"))", "line": 843, "text": "签到", "type": "string_literal"}, {"context": "add(ActiveTask(taskName = \"赠送任意U币礼物\", taskBtnLabel = \"去完成\"))", "line": 844, "text": "赠送任意U币礼物", "type": "string_literal"}, {"context": "add(ActiveTask(taskName = \"赠送任意U币礼物\", taskBtnLabel = \"去完成\"))", "line": 844, "text": "去完成", "type": "string_literal"}, {"context": "taskName = \"在群组中送1次礼物\",", "line": 847, "text": "在群组中送1次礼物", "type": "string_literal"}, {"context": "taskBtnLabel = \"已完成\",", "line": 848, "text": "已完成", "type": "string_literal"}, {"context": "taskName = \"与CP在群组中共同发言\",", "line": 854, "text": "与CP在群组中共同发言", "type": "string_literal"}, {"context": "taskBtnLabel = \"已完成\",", "line": 855, "text": "已完成", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/crony/CronySettingsScreen.kt": [{"context": "\"解除关系\",", "line": 91, "text": "解除关系", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/crony/CronyUI.kt": [{"context": "CronyTag(name = \"你大爷\"),", "line": 475, "text": "你大爷", "type": "string_literal"}, {"context": "CronyTag(name = \"你二爷\"),", "line": 476, "text": "你二爷", "type": "string_literal"}, {"context": "CronyTag(name = \"你三爷\"),", "line": 477, "text": "你三爷", "type": "string_literal"}, {"context": "CronyTag(name = \"你四爷\"),", "line": 478, "text": "你四爷", "type": "string_literal"}, {"context": "CronyTag(name = \"你五爷\"),", "line": 479, "text": "你五爷", "type": "string_literal"}, {"context": "CronyTag(name = \"你六爷\"),", "line": 480, "text": "你六爷", "type": "string_literal"}, {"context": "CronyTag(name = \"你七爷\"),", "line": 481, "text": "你七爷", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugAccountManger.kt": [{"context": "text = \"账号管理\",", "line": 119, "text": "账号管理", "type": "string_literal"}, {"context": "contentDescription = \"关闭\",", "line": 129, "text": "关闭", "type": "string_literal"}, {"context": "Text(text = \"新增账号\", style = MaterialTheme.typography.labelLarge)", "line": 136, "text": "新增账号", "type": "string_literal"}, {"context": "placeholder = \"手机号\",", "line": 157, "text": "手机号", "type": "string_literal"}, {"context": "placeholder = \"备注\",", "line": 171, "text": "备注", "type": "string_literal"}, {"context": "showToast(\"手机号必须是11位\")", "line": 181, "text": "手机号必须是11位", "type": "string_literal"}, {"context": "showToast(\"账号已存在\")", "line": 185, "text": "账号已存在", "type": "string_literal"}, {"context": "showToast(\"已保存\")", "line": 192, "text": "已保存", "type": "string_literal"}, {"context": "contentDescription = \"新增\",", "line": 196, "text": "新增", "type": "string_literal"}, {"context": "Text(text = \"自动注册号前缀和后缀（自动随机中间数字补齐11位）\", style = MaterialTheme.typography.labelLarge)", "line": 203, "text": "自动注册号前缀和后缀（自动随机中间数字补齐11位）", "type": "string_literal"}, {"context": "placeholder = \"注册号段前缀，默认是000开头\",", "line": 239, "text": "注册号段前缀，默认是000开头", "type": "string_literal"}, {"context": "placeholder = \"注册号段后缀，默认是空结尾\",", "line": 253, "text": "注册号段后缀，默认是空结尾", "type": "string_literal"}, {"context": "text = if (selectedOptionMale) \"男号配置\" else \"女号配置\",", "line": 268, "text": "男号配置", "type": "string_literal"}, {"context": "text = if (selectedOptionMale) \"男号配置\" else \"女号配置\",", "line": 268, "text": "女号配置", "type": "string_literal"}, {"context": "text = { Text(if (it == 0) \"男号配置\" else \"女号配置\") },", "line": 279, "text": "男号配置", "type": "string_literal"}, {"context": "text = { Text(if (it == 0) \"男号配置\" else \"女号配置\") },", "line": 279, "text": "女号配置", "type": "string_literal"}, {"context": "showToast(\"前缀或后缀长度不能大于4\")", "line": 291, "text": "前缀或后缀长度不能大于4", "type": "string_literal"}, {"context": "showToast(\"已保存\")", "line": 296, "text": "已保存", "type": "string_literal"}, {"context": "contentDescription = \"保存\",", "line": 300, "text": "保存", "type": "string_literal"}, {"context": "Text(text = \"账号列表\", style = MaterialTheme.typography.labelLarge)", "line": 308, "text": "账号列表", "type": "string_literal"}, {"context": "showToast(\"已复制\")", "line": 331, "text": "已复制", "type": "string_literal"}, {"context": "\"${it.account}(当前账号)\"", "line": 337, "text": "${it.account}(当前账号)", "type": "string_literal"}, {"context": "Text(text = \"删除\", style = MaterialTheme.typography.labelLarge)", "line": 352, "text": "删除", "type": "string_literal"}, {"context": "Text(text = \"切换\", style = MaterialTheme.typography.labelLarge)", "line": 365, "text": "切换", "type": "string_literal"}, {"context": "Text(\"关闭\")", "line": 381, "text": "关闭", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugEnvironmentSwitchDialog.kt": [{"context": "text = \"环境切换\",", "line": 93, "text": "环境切换", "type": "string_literal"}, {"context": "contentDescription = \"关闭\",", "line": 101, "text": "关闭", "type": "string_literal"}, {"context": "text = \"当前环境\",", "line": 120, "text": "当前环境", "type": "string_literal"}, {"context": "text = \"可用环境\",", "line": 138, "text": "可用环境", "type": "string_literal"}, {"context": "text = \"没有可用的环境配置\",", "line": 164, "text": "没有可用的环境配置", "type": "string_literal"}, {"context": "Text(\"重置\")", "line": 191, "text": "重置", "type": "string_literal"}, {"context": "Text(\"保存\")", "line": 205, "text": "保存", "type": "string_literal"}, {"context": "contentDescription = \"已选中\",", "line": 279, "text": "已选中", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugFloatingButton.kt": [{"context": "\"调试工具\",", "line": 86, "text": "调试工具", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugGitCommitsDialog.kt": [{"context": "text = \"Git 提交记录\",", "line": 101, "text": "Git 提交记录", "type": "string_literal"}, {"context": "contentDescription = \"关闭\",", "line": 109, "text": "关闭", "type": "string_literal"}, {"context": "text = \"无法获取 Git 提交记录\",", "line": 126, "text": "无法获取 Git 提交记录", "type": "string_literal"}, {"context": "showToast(\"已复制\")", "line": 145, "text": "已复制", "type": "string_literal"}, {"context": "Text(\"关闭\")", "line": 160, "text": "关闭", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugMenu.kt": [{"context": "text = \"调试菜单\",", "line": 111, "text": "调试菜单", "type": "string_literal"}, {"context": "contentDescription = \"关闭\",", "line": 119, "text": "关闭", "type": "string_literal"}, {"context": "text = \"账号信息\",", "line": 144, "text": "账号信息", "type": "string_literal"}, {"context": "InfoRow(\"手机号\", DevicesKV.decodeString(\"_debug_current_phone\").orEmpty())", "line": 161, "text": "手机号", "type": "string_literal"}, {"context": "InfoRow(\"设备id\", DeviceInfoManager.deviceId)", "line": 162, "text": "设备id", "type": "string_literal"}, {"context": "InfoRow(\"是否是主播\", SelfUser?.isHQU?.toString().orEmpty())", "line": 164, "text": "是否是主播", "type": "string_literal"}, {"context": "InfoRow(\"是否是日区\", SelfUser?.isJP?.toString().orEmpty())", "line": 165, "text": "是否是日区", "type": "string_literal"}, {"context": "text = \"应用信息\",", "line": 190, "text": "应用信息", "type": "string_literal"}, {"context": "InfoRow(\"版本\", \"${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})\")", "line": 207, "text": "版本", "type": "string_literal"}, {"context": "InfoRow(\"构建类型\", BuildConfig.BUILD_TYPE)", "line": 208, "text": "构建类型", "type": "string_literal"}, {"context": "InfoRow(\"构建环境\", BuildConfig.FLAVOR_environment)", "line": 209, "text": "构建环境", "type": "string_literal"}, {"context": "InfoRow(\"渠道\", BuildConfig.FLAVOR_channel)", "line": 210, "text": "渠道", "type": "string_literal"}, {"context": "InfoRow(\"当前环境\", EnvironmentManager.getCurrentEnvironmentKey())", "line": 211, "text": "当前环境", "type": "string_literal"}, {"context": "text = \"Git 信息\",", "line": 236, "text": "Git 信息", "type": "string_literal"}, {"context": "InfoRow(\"分支\", BuildConfig.GIT_BRANCH)", "line": 255, "text": "分支", "type": "string_literal"}, {"context": "InfoRow(\"提交\", BuildConfig.GIT_SHORT_COMMIT_HASH)", "line": 256, "text": "提交", "type": "string_literal"}, {"context": "InfoRow(\"消息\", BuildConfig.GIT_COMMIT_MESSAGE)", "line": 257, "text": "消息", "type": "string_literal"}, {"context": "InfoRow(\"作者\", BuildConfig.GIT_COMMIT_AUTHOR)", "line": 258, "text": "作者", "type": "string_literal"}, {"context": "InfoRow(\"提交时间\", BuildConfig.GIT_COMMIT_DATE)", "line": 259, "text": "提交时间", "type": "string_literal"}, {"context": "\"构建时间\",", "line": 261, "text": "构建时间", "type": "string_literal"}, {"context": "text = \"调试功能\",", "line": 273, "text": "调试功能", "type": "string_literal"}, {"context": "title = \"环境切换\",", "line": 291, "text": "环境切换", "type": "string_literal"}, {"context": "description = \"切换到不同的环境配置\",", "line": 292, "text": "切换到不同的环境配置", "type": "string_literal"}, {"context": "title = \"Git 提交记录\",", "line": 301, "text": "Git 提交记录", "type": "string_literal"}, {"context": "description = \"查看最近5条提交记录\",", "line": 302, "text": "查看最近5条提交记录", "type": "string_literal"}, {"context": "title = \"日志开关: ${if (EnvironmentManager.current.enableLog) \"on\" else \"off\"}\",", "line": 311, "text": "日志开关: ${if (EnvironmentManager.current.enableLog) ", "type": "string_literal"}, {"context": "description = \"切换将重启app\",", "line": 312, "text": "切换将重启app", "type": "string_literal"}, {"context": "description = \"调试页面\",", "line": 322, "text": "调试页面", "type": "string_literal"}, {"context": "title = \"退出登录\",", "line": 332, "text": "退出登录", "type": "string_literal"}, {"context": "description = \"返回到登录页面\",", "line": 333, "text": "返回到登录页面", "type": "string_literal"}, {"context": "title = \"账号管理\",", "line": 345, "text": "账号管理", "type": "string_literal"}, {"context": "description = \"管理保存常用号码方便切换\",", "line": 346, "text": "管理保存常用号码方便切换", "type": "string_literal"}, {"context": "title = \"一键注册女号\",", "line": 356, "text": "一键注册女号", "type": "string_literal"}, {"context": "description = \"手机号随机(可以设置号段)\",", "line": 357, "text": "手机号随机(可以设置号段)", "type": "string_literal"}, {"context": "title = \"一键注册男号\",", "line": 378, "text": "一键注册男号", "type": "string_literal"}, {"context": "description = \"手机号随机(可以设置号段)\",", "line": 379, "text": "手机号随机(可以设置号段)", "type": "string_literal"}, {"context": "Text(\"关闭\")", "line": 406, "text": "关闭", "type": "string_literal"}, {"context": "showToast(\"已复制\")", "line": 460, "text": "已复制", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugScreen.kt": [{"context": "Text(\"H5 测试\")", "line": 83, "text": "H5 测试", "type": "string_literal"}, {"context": "Text(\"本地H5 测试\")", "line": 88, "text": "本地H5 测试", "type": "string_literal"}, {"context": "Text(\"PUQC测试\")", "line": 102, "text": "PUQC测试", "type": "string_literal"}, {"context": "Text(\"装扮商城\")", "line": 113, "text": "装扮商城", "type": "string_literal"}, {"context": "SolidButton(\"群组\", onClick = {", "line": 116, "text": "群组", "type": "string_literal"}, {"context": "SolidButton(\"打开群组\", onClick = {", "line": 127, "text": "打开群组", "type": "string_literal"}, {"context": "SolidButton(\"打开个人主页\", onClick = {", "line": 131, "text": "打开个人主页", "type": "string_literal"}, {"context": "Text(\"打开用户主页\")", "line": 138, "text": "打开用户主页", "type": "string_literal"}, {"context": "Text(\"打开提现\")", "line": 147, "text": "打开提现", "type": "string_literal"}, {"context": "Text(\"打开Report\")", "line": 152, "text": "打开Report", "type": "string_literal"}, {"context": "Text(\"打开通知\")", "line": 158, "text": "打开通知", "type": "string_literal"}, {"context": "Text(\"打开图库\")", "line": 238, "text": "打开图库", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/dressup/SilverShopListScreen.kt": [{"context": "text = \"总计需要消耗银币：%s\\n兑换成功后将发放至礼物背包\".localizedFormatWithKey(\"总计需要消耗银币\", totalPrice),", "line": 691, "text": "总计需要消耗银币", "type": "string_literal"}, {"context": "ExchangeItemsDialogContent(\"\", \"基督教\", 100) {}", "line": 741, "text": "基督教", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/home/<USER>": [{"context": "contentDescription = \"钻石\",", "line": 539, "text": "钻石", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/japan/boost/BoostWidgets.kt": [{"context": "PWidget(\"379191\", \"说明：钻石可用于App内购买卡片背景，装扮，礼物等道具\", \"兑换现金\", recordVisible = true, qaVisible = true)", "line": 518, "text": "说明：钻石可用于App内购买卡片背景，装扮，礼物等道具", "type": "string_literal"}, {"context": "CWidget(\"我的现金\", \"23929\", \"出金\")", "line": 520, "text": "我的现金", "type": "string_literal"}, {"context": "CWidget(\"我的现金\", \"23929\", \"出金\")", "line": 520, "text": "出金", "type": "string_literal"}, {"context": "\"友達を招待して登録し、 ダイヤをゲット\",", "line": 523, "text": "友達を招待して登録し、 ダイヤをゲット", "type": "string_literal"}, {"context": "\"友達を1人招待するごとに、5000ダイヤが報酬としてもらえます。 友達がUCOOでコインをチャージしたり、ダイヤをゲットしたりすると、あなたはダイヤがもらえます。\",", "line": 524, "text": "友達を1人招待するごとに、5000ダイヤが報酬としてもらえます。 友達がUCOOでコインをチャージしたり、ダイヤをゲットしたりすると、あなたはダイヤがもらえます。", "type": "string_literal"}, {"context": "\"今すぐ招待\",", "line": 525, "text": "今すぐ招待", "type": "string_literal"}, {"context": "MissionCompleteContent(\"50积分\", \"完成“填写所有个人基础信息”任务，获得积分奖励\") { }", "line": 621, "text": "50积分", "type": "string_literal"}, {"context": "MissionCompleteContent(\"50积分\", \"完成“填写所有个人基础信息”任务，获得积分奖励\") { }", "line": 621, "text": "完成“填写所有个人基础信息”任务，获得积分奖励", "type": "string_literal"}, {"context": "MissionCompleteContent(\"50钻石\", \"完成“填写所有个人基础信息”任务，获得积分奖励\", awardType = AwardType.dia) { }", "line": 622, "text": "50钻石", "type": "string_literal"}, {"context": "MissionCompleteContent(\"50钻石\", \"完成“填写所有个人基础信息”任务，获得积分奖励\", awardType = AwardType.dia) { }", "line": 622, "text": "完成“填写所有个人基础信息”任务，获得积分奖励", "type": "string_literal"}, {"context": "InputInviteCodeContent(\"如果你是通过好友推荐下载的本APP，你可以填写好友的邀请码，可立即领取xxx钻石奖励\") { }", "line": 673, "text": "如果你是通过好友推荐下载的本APP，你可以填写好友的邀请码，可立即领取xxx钻石奖励", "type": "string_literal"}, {"context": "ExchangeContent(\"积分兑换现金\", \"确认兑换\", centerContent = {", "line": 758, "text": "积分兑换现金", "type": "string_literal"}, {"context": "ExchangeContent(\"积分兑换现金\", \"确认兑换\", centerContent = {", "line": 758, "text": "确认兑换", "type": "string_literal"}, {"context": "\"12000积分兑换\",", "line": 772, "text": "12000积分兑换", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/japan/boost/SignContent.kt": [{"context": "contentDescription = \"关闭\",", "line": 110, "text": "关闭", "type": "string_literal"}, {"context": "contentDescription = \"已签到\",", "line": 224, "text": "已签到", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/liveroom/HongBaoUI.kt": [{"context": "Text(\"可抢用户\", color = Color.White.copy(0.8f), modifier = Modifier.align(Alignment.CenterStart))", "line": 407, "text": "可抢用户", "type": "string_literal"}, {"context": "\"2-50个\",", "line": 683, "text": "2-50个", "type": "string_literal"}, {"context": "\"50钻石起\",", "line": 684, "text": "50钻石起", "type": "string_literal"}, {"context": "\"红包钻石数>999将触发全服飘屏通知\",", "line": 685, "text": "红包钻石数>999将触发全服飘屏通知", "type": "string_literal"}, {"context": "\"未领取的红包过期后会发起退款，2人红包将扣除5%钻石手续费\",", "line": 687, "text": "未领取的红包过期后会发起退款，2人红包将扣除5%钻石手续费", "type": "string_literal"}, {"context": "\"幼儿园的爆红hahahhahaahahhaahhahahaahah的红包\",", "line": 781, "text": "幼儿园的爆红hahahhahaahahhaahhahahaahah的红包", "type": "string_literal"}, {"context": "\"恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,\",", "line": 782, "text": "恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,", "type": "string_literal"}, {"context": "\"本群组成员可抢\",", "line": 783, "text": "本群组成员可抢", "type": "string_literal"}, {"context": "\"红包金额：52钻石\",", "line": 784, "text": "红包金额：52钻石", "type": "string_literal"}, {"context": "lastTimeDesc = \"02:49后开抢\",", "line": 785, "text": "02:49后开抢", "type": "string_literal"}, {"context": "Text(\"恭喜，抢到24钻石\", fontSize = 18.sp, color = Color(0xFFCA803A))", "line": 831, "text": "恭喜，抢到24钻石", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/liveroom/RoomBottomLayout.kt": [{"context": "contentDescription = \"礼物\",", "line": 206, "text": "礼物", "type": "string_literal"}, {"context": "contentDescription = \"礼物\",", "line": 282, "text": "礼物", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/liveroom/RoomPassword.kt": [{"context": "title = \"请输入密码\",", "line": 254, "text": "请输入密码", "type": "string_literal"}, {"context": "buttonText = \"确认\",", "line": 255, "text": "确认", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/liveroom/screen/InviteToPrivateRoomScreen.kt": [{"context": "append(\"丨\")", "line": 170, "text": "丨", "type": "string_literal"}, {"context": "append(\"丨\")", "line": 175, "text": "丨", "type": "string_literal"}, {"context": "InviteToPrivateRoomScreen(GuideInfo(user = BasicUser.sampleGirl, desc = \"她正在找人语音连麦聊天...\"), false)", "line": 282, "text": "她正在找人语音连麦聊天...", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/login/LoginHostScreen.kt": [{"context": "showToast(\"该账号未注册, 切换失败\")", "line": 60, "text": "该账号未注册, 切换失败", "type": "string_literal"}, {"context": "\"男$mask\",", "line": 89, "text": "男$mask", "type": "string_literal"}, {"context": "\"女$mask\",", "line": 95, "text": "女$mask", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/login/LoginScreen.kt": [{"context": "text = \"账号管理\",", "line": 132, "text": "账号管理", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/messages/chat/BuddyUI.kt": [{"context": "BuddyCard(\"恭喜你们成功结成CP，解锁CP空间\", \"查看CP空间\")", "line": 339, "text": "恭喜你们成功结成CP，解锁CP空间", "type": "string_literal"}, {"context": "BuddyCard(\"恭喜你们成功结成CP，解锁CP空间\", \"查看CP空间\")", "line": 339, "text": "查看CP空间", "type": "string_literal"}, {"context": "\"对方赠送了CP表白礼物，希望和你结成线上虚拟情侣，邀请24小时后失效\",", "line": 342, "text": "对方赠送了CP表白礼物，希望和你结成线上虚拟情侣，邀请24小时后失效", "type": "string_literal"}, {"context": "\"查看邀请\",", "line": 343, "text": "查看邀请", "type": "string_literal"}, {"context": "\"恋爱君温馨提示：你们关系升级的很快呢！点击下方按钮可与TA结成线上虚拟情侣，解锁更多亲密互动玩法\",", "line": 351, "text": "恋爱君温馨提示：你们关系升级的很快呢！点击下方按钮可与TA结成线上虚拟情侣，解锁更多亲密互动玩法", "type": "string_literal"}, {"context": "\"去组CP\",", "line": 352, "text": "去组CP", "type": "string_literal"}, {"context": "CPCard(BasicUser.sampleBoy, BasicUser.sampleGirl, \"2200\", daysDesc = \"在一起120天\", modifier = Modifier.padding(16.dp))", "line": 375, "text": "在一起120天", "type": "string_literal"}, {"context": "\"恭喜【无敌小霸王】和【幼儿园搬花】获得1个CP盲盒！\",", "line": 846, "text": "恭喜【无敌小霸王】和【幼儿园搬花】获得1个CP盲盒！", "type": "string_literal"}, {"context": "\"开盲盒送给CP\",", "line": 847, "text": "开盲盒送给CP", "type": "string_literal"}, {"context": "\"999钻石\",", "line": 848, "text": "999钻石", "type": "string_literal"}, {"context": "append(\"与CP共同挂麦00:01s后可获得\")", "line": 900, "text": "与CP共同挂麦00:01s后可获得", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/messages/notification/NotificationListItem.kt": [{"context": "name = \"W<PERSON>oo官方通知\",", "line": 175, "text": "Wakoo官方通知", "type": "string_literal"}, {"context": "message = \"我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...\",", "line": 176, "text": "我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...", "type": "string_literal"}, {"context": "time = \"1分钟前\",", "line": 177, "text": "1分钟前", "type": "string_literal"}, {"context": "name = \"幼儿园搬花\",", "line": 184, "text": "幼儿园搬花", "type": "string_literal"}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 185, "text": "我在这里等了好久，你终于出现啦！", "type": "string_literal"}, {"context": "time = \"1分钟前\",", "line": 186, "text": "1分钟前", "type": "string_literal"}, {"context": "name = \"幼儿园搬花\",", "line": 192, "text": "幼儿园搬花", "type": "string_literal"}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 193, "text": "我在这里等了好久，你终于出现啦！", "type": "string_literal"}, {"context": "time = \"1分钟前\",", "line": 194, "text": "1分钟前", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/messages/notification/NotificationPage.kt": [{"context": "name = \"W<PERSON>oo官方通知\",", "line": 54, "text": "Wakoo官方通知", "type": "string_literal"}, {"context": "message = \"我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...\",", "line": 55, "text": "我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...", "type": "string_literal"}, {"context": "time = \"1分钟前\",", "line": 56, "text": "1分钟前", "type": "string_literal"}, {"context": "name = \"幼儿园搬花\",", "line": 63, "text": "幼儿园搬花", "type": "string_literal"}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 64, "text": "我在这里等了好久，你终于出现啦！", "type": "string_literal"}, {"context": "time = \"1分钟前\",", "line": 65, "text": "1分钟前", "type": "string_literal"}, {"context": "name = \"幼儿园搬花\",", "line": 71, "text": "幼儿园搬花", "type": "string_literal"}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 72, "text": "我在这里等了好久，你终于出现啦！", "type": "string_literal"}, {"context": "time = \"1分钟前\",", "line": 73, "text": "1分钟前", "type": "string_literal"}, {"context": "name = \"幼儿园搬花\",", "line": 78, "text": "幼儿园搬花", "type": "string_literal"}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 79, "text": "我在这里等了好久，你终于出现啦！", "type": "string_literal"}, {"context": "time = \"1分钟前\",", "line": 80, "text": "1分钟前", "type": "string_literal"}, {"context": "name = \"幼儿园搬花\",", "line": 85, "text": "幼儿园搬花", "type": "string_literal"}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 86, "text": "我在这里等了好久，你终于出现啦！", "type": "string_literal"}, {"context": "time = \"1分钟前\",", "line": 87, "text": "1分钟前", "type": "string_literal"}, {"context": "name = \"W<PERSON>oo官方通知\",", "line": 98, "text": "Wakoo官方通知", "type": "string_literal"}, {"context": "message = \"系统通知：您的账号已激活！\",", "line": 99, "text": "系统通知：您的账号已激活！", "type": "string_literal"}, {"context": "time = \"1分钟前\",", "line": 100, "text": "1分钟前", "type": "string_literal"}, {"context": "name = \"幼儿园搬花\",", "line": 106, "text": "幼儿园搬花", "type": "string_literal"}, {"context": "message = \"关注了你\",", "line": 107, "text": "关注了你", "type": "string_literal"}, {"context": "time = \"10分钟前\",", "line": 108, "text": "10分钟前", "type": "string_literal"}, {"context": "name = \"幼儿园搬花\",", "line": 113, "text": "幼儿园搬花", "type": "string_literal"}, {"context": "message = \"喜欢了你的作品\",", "line": 114, "text": "喜欢了你的作品", "type": "string_literal"}, {"context": "time = \"1小时前\",", "line": 115, "text": "1小时前", "type": "string_literal"}, {"context": "title = \"私信\",", "line": 159, "text": "私信", "type": "string_literal"}, {"context": "title = \"通知\",", "line": 169, "text": "通知", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/profile/ProfileVoiceEditScreen.kt": [{"context": "message = \"删除文件失败\",", "line": 218, "text": "删除文件失败", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/profile/UserProfileUI.kt": [{"context": "text = \"${group.memberCnt}人\",", "line": 935, "text": "${group.memberCnt}人", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/recharge/RechargeRecordScreen.kt": [{"context": "\"充值\",", "line": 142, "text": "充值", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/vip/VipPage.kt": [{"context": "contentDescription = \"用户头像\",", "line": 322, "text": "用户头像", "type": "string_literal"}, {"context": "contentDescription = \"VIP皇冠\",", "line": 377, "text": "VIP皇冠", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/screens/voice/VoicePublishScreen.kt": [{"context": "\"\"\".trimIndent().localizedWithKey(\"语音发布社区规则\")", "line": 134, "text": "语音发布社区规则", "type": "string_literal"}, {"context": "message = \"删除文件失败\",", "line": 323, "text": "删除文件失败", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/BasicUI.kt": [{"context": "contentDescription = \"返回\",", "line": 325, "text": "返回", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/ButtonUI.kt": [{"context": "text = \"退出登录\",", "line": 383, "text": "退出登录", "type": "string_literal"}, {"context": "text = \"注销账号\",", "line": 389, "text": "注销账号", "type": "string_literal"}, {"context": "text = \"关注她\",", "line": 395, "text": "关注她", "type": "string_literal"}, {"context": "text = \"自定义实心按钮\",", "line": 401, "text": "自定义实心按钮", "type": "string_literal"}, {"context": "text = \"禁用状态\",", "line": 409, "text": "禁用状态", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/ExpandableText.kt": [{"context": "\"在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹。在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹吧...\"", "line": 236, "text": "在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹。在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹吧...", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/QuoteBorderBox.kt": [{"context": "val shortText = \"有時話不多，是因為想得太多。\\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。\"", "line": 284, "text": "有時話不多，是因為想得太多。\\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。", "type": "string_literal"}, {"context": "\"有時話不多，是因為想得太多。\\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。有時話不多，是因為想得太多。喜歡安ंधी的對話，也期待有人能讀懂沉默裡的內心。\"", "line": 286, "text": "有時話不多，是因為想得太多。\\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。有時話不多，是因為想得太多。喜歡安ंधी的對話，也期待有人能讀懂沉默裡的內心。", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/TextUI.kt": [{"context": "SkewedGradientText(\"关注TA\", true)", "line": 83, "text": "关注TA", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/UserUI.kt": [{"context": "* 一个简单的文本标签组件，用于显示像 \"#唱歌\" 这样的内容。", "line": 112, "text": "#唱歌", "type": "string_literal"}, {"context": "TextTagChip(text = \"#唱歌\")", "line": 192, "text": "#唱歌", "type": "string_literal"}, {"context": "VoiceTagChip(false, tag = VoiceTag(name = \"树洞\"))", "line": 199, "text": "树洞", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/VoicePlayingAnimation.kt": [{"context": "contentDescription = if (isPlaying) \"语音播放中\" else \"语音已停止\",", "line": 85, "text": "语音播放中", "type": "string_literal"}, {"context": "contentDescription = if (isPlaying) \"语音播放中\" else \"语音已停止\",", "line": 85, "text": "语音已停止", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/VolumeRippleEffect.kt": [{"context": "contentDescription = \"用户头像\",", "line": 198, "text": "用户头像", "type": "string_literal"}, {"context": "text = \"模拟音量: ${(volume * 100).toInt()}%\",", "line": 209, "text": "模拟音量: ${(volume * 100).toInt()}%", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/gift/GiftPanel.kt": [{"context": "add(GiftBean(icon = \"\", name = \"礼物${this.size}\", price = 100, id = this.size))", "line": 1479, "text": "礼物${this.size}", "type": "string_literal"}, {"context": "add(GiftBean(icon = \"\", name = \"礼物${this.size}\", price = 100, id = this.size, superscriptIcon = \"asdf\"))", "line": 1530, "text": "礼物${this.size}", "type": "string_literal"}, {"context": "add(GiftBean(icon = \"\", name = \"礼物${this.size}\", price = 100, id = this.size))", "line": 1585, "text": "礼物${this.size}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/gift/GiftWallWidget.kt": [{"context": "LogUtils.w(\"GiftWallScreen\", \"未知的操作purpose\")", "line": 588, "text": "未知的操作purpose", "type": "string_literal"}, {"context": "gift = GiftWall.GiftWrapper.Gift(t = 0, id = 311, name = \"测试礼物\"),", "line": 887, "text": "测试礼物", "type": "string_literal"}, {"context": "seriesName = \"测试礼物$j\",", "line": 897, "text": "测试礼物$j", "type": "string_literal"}, {"context": "GiftWallSummaryBean.Tab(t = 0, name = \"盲盒礼物\"),", "line": 914, "text": "盲盒礼物", "type": "string_literal"}, {"context": "GiftWallSummaryBean.Tab(t = 1, name = \"普通礼物\"),", "line": 915, "text": "普通礼物", "type": "string_literal"}, {"context": "GiftWallSummaryBean.Tab(t = 1, name = \"普通礼物\"),", "line": 916, "text": "普通礼物", "type": "string_literal"}, {"context": "GiftWallSummaryBean.Tab(t = 1, name = \"普通礼物\"),", "line": 917, "text": "普通礼物", "type": "string_literal"}, {"context": "GiftWallSummaryBean.Tab(t = 1, name = \"普通礼物\"),", "line": 918, "text": "普通礼物", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/gift/YYEVAResourceFetcher.kt": [{"context": "LogUtils.w(\"loadImageSync: 加载成功 - $url\")", "line": 131, "text": "loadImageSync: 加载成功 - $url", "type": "string_literal"}, {"context": "LogUtils.w(\"loadImageSync: 加载失败 - $url, error: ${result.throwable}\")", "line": 136, "text": "loadImageSync: 加载失败 - $url, error: ${result.throwable}", "type": "string_literal"}, {"context": "LogUtils.w(\"loadImageSync: 异常 - $url, error: ${e.message}\")", "line": 142, "text": "loadImageSync: 异常 - $url, error: ${e.message}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/media/data/AndroidMediaRepository.kt": [{"context": "* 4. 手动创建一个 \"所有媒体\" 的相册。", "line": 59, "text": "所有媒体", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/popup/Demo.kt": [{"context": "contentDescription = \"更多选项\",", "line": 44, "text": "更多选项", "type": "string_literal"}, {"context": "contentDescription = \"举报图标\",", "line": 70, "text": "举报图标", "type": "string_literal"}, {"context": "text = { Text(\"不感兴趣\") },", "line": 75, "text": "不感兴趣", "type": "string_literal"}, {"context": "contentDescription = \"不感兴趣图标\",", "line": 85, "text": "不感兴趣图标", "type": "string_literal"}, {"context": "Text(\"这是下面的内容\")", "line": 99, "text": "这是下面的内容", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/richtext/RichText.kt": [{"context": "append(\"欢迎使用优化后的 RichText！\")", "line": 327, "text": "欢迎使用优化后的 RichText！", "type": "string_literal"}, {"context": "append(\"现在可以高效处理已知尺寸的组件。\")", "line": 329, "text": "现在可以高效处理已知尺寸的组件。", "type": "string_literal"}, {"context": "Toast.makeText(context, \"疯狂\", Toast.LENGTH_SHORT).show()", "line": 339, "text": "疯狂", "type": "string_literal"}, {"context": "append(\"《用户协议》\")", "line": 353, "text": "《用户协议》", "type": "string_literal"}, {"context": "append(\"\\n例如，这个图标尺寸是固定的: \")", "line": 358, "text": "\\n例如，这个图标尺寸是固定的: ", "type": "string_literal"}, {"context": "append(\"\\n而对于尺寸未知的网络图片: \")", "line": 371, "text": "\\n而对于尺寸未知的网络图片: ", "type": "string_literal"}, {"context": "contentDescription = \"小狗\",", "line": 377, "text": "小狗", "type": "string_literal"}, {"context": "Toast.makeText(context, \"大结局\", Toast.LENGTH_SHORT).show()", "line": 380, "text": "大结局", "type": "string_literal"}, {"context": "append(\"，它仍然能完美处理。\")", "line": 384, "text": "，它仍然能完美处理。", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/state/StateComponent.kt": [{"context": "StateComponent.Error(text = \"请求错误\", errorId = R.drawable.ic_error_for_all) {", "line": 308, "text": "请求错误", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/DateWheelPicker.kt": [{"context": "text = String.format(\"%02d时\", hour),", "line": 521, "text": "%02d时", "type": "string_literal"}, {"context": "text = String.format(\"%02d分\", minute),", "line": 548, "text": "%02d分", "type": "string_literal"}, {"context": "text = String.format(\"%02d秒\", second),", "line": 576, "text": "%02d秒", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/WheelPickerUtils.kt": [{"context": "\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\",", "line": 192, "text": "一月", "type": "string_literal"}, {"context": "\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\",", "line": 192, "text": "二月", "type": "string_literal"}, {"context": "\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\",", "line": 192, "text": "三月", "type": "string_literal"}, {"context": "\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\",", "line": 192, "text": "四月", "type": "string_literal"}, {"context": "\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\",", "line": 192, "text": "五月", "type": "string_literal"}, {"context": "\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\",", "line": 192, "text": "六月", "type": "string_literal"}, {"context": "\"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"", "line": 193, "text": "七月", "type": "string_literal"}, {"context": "\"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"", "line": 193, "text": "八月", "type": "string_literal"}, {"context": "\"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"", "line": 193, "text": "九月", "type": "string_literal"}, {"context": "\"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"", "line": 193, "text": "十月", "type": "string_literal"}, {"context": "\"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"", "line": 193, "text": "十一月", "type": "string_literal"}, {"context": "\"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"", "line": 193, "text": "十二月", "type": "string_literal"}, {"context": "\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"", "line": 216, "text": "星期日", "type": "string_literal"}, {"context": "\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"", "line": 216, "text": "星期一", "type": "string_literal"}, {"context": "\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"", "line": 216, "text": "星期二", "type": "string_literal"}, {"context": "\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"", "line": 216, "text": "星期三", "type": "string_literal"}, {"context": "\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"", "line": 216, "text": "星期四", "type": "string_literal"}, {"context": "\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"", "line": 216, "text": "星期五", "type": "string_literal"}, {"context": "\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"", "line": 216, "text": "星期六", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/demo/EnhancedDatePickerExample.kt": [{"context": "text = \"增强版日期选择器示例\",", "line": 38, "text": "增强版日期选择器示例", "type": "string_literal"}, {"context": "text = \"基础日期选择器\",", "line": 76, "text": "基础日期选择器", "type": "string_literal"}, {"context": "text = \"选中日期: $selectedDate\",", "line": 90, "text": "选中日期: $selectedDate", "type": "string_literal"}, {"context": "text = \"限制到今天的日期选择器\",", "line": 116, "text": "限制到今天的日期选择器", "type": "string_literal"}, {"context": "text = \"只能选择今天及之前的日期\",", "line": 122, "text": "只能选择今天及之前的日期", "type": "string_literal"}, {"context": "text = \"选中日期: $selectedDate\",", "line": 137, "text": "选中日期: $selectedDate", "type": "string_literal"}, {"context": "text = \"循环滚动日期选择器\",", "line": 164, "text": "循环滚动日期选择器", "type": "string_literal"}, {"context": "text = \"月份和日期支持循环滚动，日期固定显示31天（不存在的日期会变灰）\",", "line": 170, "text": "月份和日期支持循环滚动，日期固定显示31天（不存在的日期会变灰）", "type": "string_literal"}, {"context": "text = \"选中日期: $selectedDate\",", "line": 187, "text": "选中日期: $selectedDate", "type": "string_literal"}, {"context": "text = \"自定义范围日期选择器\",", "line": 222, "text": "自定义范围日期选择器", "type": "string_literal"}, {"context": "text = \"只能选择最近30天，月份循环滚动，日期固定31天\",", "line": 228, "text": "只能选择最近30天，月份循环滚动，日期固定31天", "type": "string_literal"}, {"context": "text = \"选中日期: $selectedDate\",", "line": 245, "text": "选中日期: $selectedDate", "type": "string_literal"}, {"context": "text = \"可用范围: ${customDateRange.start} 到 ${customDateRange.endInclusive}\",", "line": 251, "text": "可用范围: ${customDateRange.start} 到 ${customDateRange.endInclusive}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/demo/SimpleWheelPickerTest.kt": [{"context": "text = \"WheelPicker 测试\",", "line": 30, "text": "WheelPicker 测试", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 38, "text": "苹果", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 38, "text": "香蕉", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 38, "text": "橙子", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 38, "text": "葡萄", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 38, "text": "草莓", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 38, "text": "蓝莓", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 38, "text": "芒果", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 38, "text": "菠萝", "type": "string_literal"}, {"context": "text = \"选中项: $selectedItem\",", "line": 63, "text": "选中项: $selectedItem", "type": "string_literal"}, {"context": "text = \"最终索引: ${state.snappedIndex}\",", "line": 71, "text": "最终索引: ${state.snappedIndex}", "type": "string_literal"}, {"context": "text = \"当前索引: ${state.currentIndex}\",", "line": 76, "text": "当前索引: ${state.currentIndex}", "type": "string_literal"}, {"context": "text = \"是否滚动中: ${state.isScrollInProgress}\",", "line": 81, "text": "是否滚动中: ${state.isScrollInProgress}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/demo/WheelPickerEnabledExample.kt": [{"context": "text = \"可用性检查示例\",", "line": 39, "text": "可用性检查示例", "type": "string_literal"}, {"context": "text = \"日期选择器（只能选择今天及之前）\",", "line": 73, "text": "日期选择器（只能选择今天及之前）", "type": "string_literal"}, {"context": "text = \"${year}年\",", "line": 151, "text": "${year}年", "type": "string_literal"}, {"context": "text = \"${month}月\",", "line": 173, "text": "${month}月", "type": "string_literal"}, {"context": "text = \"${day}日\",", "line": 195, "text": "${day}日", "type": "string_literal"}, {"context": "text = \"选中日期: $selectedDate\",", "line": 210, "text": "选中日期: $selectedDate", "type": "string_literal"}, {"context": "text = \"数字选择器（只能选择偶数）\",", "line": 234, "text": "数字选择器（只能选择偶数）", "type": "string_literal"}, {"context": "text = \"选中数字: $selectedNumber\",", "line": 277, "text": "选中数字: $selectedNumber", "type": "string_literal"}, {"context": "text = \"提示: 奇数不可选择，滚动到奇数会自动跳转到最近的偶数\",", "line": 283, "text": "提示: 奇数不可选择，滚动到奇数会自动跳转到最近的偶数", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/demo/WheelPickerExamples.kt": [{"context": "text = \"WheelPicker 示例\",", "line": 53, "text": "WheelPicker 示例", "type": "string_literal"}, {"context": "text = \"基础字符串选择器\",", "line": 91, "text": "基础字符串选择器", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 98, "text": "苹果", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 98, "text": "香蕉", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 98, "text": "橙子", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 98, "text": "葡萄", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 98, "text": "草莓", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 98, "text": "蓝莓", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 98, "text": "芒果", "type": "string_literal"}, {"context": "listOf(\"苹果\", \"香蕉\", \"橙子\", \"葡萄\", \"草莓\", \"蓝莓\", \"芒果\", \"菠萝\")", "line": 98, "text": "菠萝", "type": "string_literal"}, {"context": "text = \"选中项: $selectedItem\",", "line": 121, "text": "选中项: $selectedItem", "type": "string_literal"}, {"context": "text = \"数字选择器（无限循环）\",", "line": 145, "text": "数字选择器（无限循环）", "type": "string_literal"}, {"context": "text = \"选中数字: $selectedNumber\",", "line": 188, "text": "选中数字: $selectedNumber", "type": "string_literal"}, {"context": "text = \"日期选择器\",", "line": 213, "text": "日期选择器", "type": "string_literal"}, {"context": "text = \"选中日期: $selectedDate\",", "line": 228, "text": "选中日期: $selectedDate", "type": "string_literal"}, {"context": "text = \"时间选择器\",", "line": 254, "text": "时间选择器", "type": "string_literal"}, {"context": "text = \"选中时间: ${WheelPickerUtils.formatTime(selectedHour, selectedMinute, selectedSecond)}\",", "line": 276, "text": "选中时间: ${WheelPickerUtils.formatTime(selectedHour, selectedMinute, selectedSecond)}", "type": "string_literal"}, {"context": "text = \"自定义样式选择器\",", "line": 300, "text": "自定义样式选择器", "type": "string_literal"}, {"context": "listOf(\"小\", \"中\", \"大\", \"特大\", \"超大\")", "line": 307, "text": "小", "type": "string_literal"}, {"context": "listOf(\"小\", \"中\", \"大\", \"特大\", \"超大\")", "line": 307, "text": "中", "type": "string_literal"}, {"context": "listOf(\"小\", \"中\", \"大\", \"特大\", \"超大\")", "line": 307, "text": "大", "type": "string_literal"}, {"context": "listOf(\"小\", \"中\", \"大\", \"特大\", \"超大\")", "line": 307, "text": "特大", "type": "string_literal"}, {"context": "listOf(\"小\", \"中\", \"大\", \"特大\", \"超大\")", "line": 307, "text": "超大", "type": "string_literal"}, {"context": "text = \"选中尺寸: $selectedItem\",", "line": 359, "text": "选中尺寸: $selectedItem", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/utils/CommonSaver.kt": [{"context": "*         mutableStateOf(UserInfo(\"123\", \"张三\"))", "line": 52, "text": "张三", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/utils/DateTimeUtils.kt": [{"context": "* 例如：\"刚刚\"、\"5分钟前\"、\"2小时前\"等", "line": 87, "text": "刚刚", "type": "string_literal"}, {"context": "* 例如：\"刚刚\"、\"5分钟前\"、\"2小时前\"等", "line": 87, "text": "5分钟前", "type": "string_literal"}, {"context": "* 例如：\"刚刚\"、\"5分钟前\"、\"2小时前\"等", "line": 87, "text": "2小时前", "type": "string_literal"}, {"context": "diff < 60 -> \"刚刚\"", "line": 97, "text": "刚刚", "type": "string_literal"}, {"context": "diff < 3600 -> \"${diff / 60}分钟前\"", "line": 98, "text": "${diff / 60}分钟前", "type": "string_literal"}, {"context": "diff < 86400 -> \"${diff / 3600}小时前\"", "line": 99, "text": "${diff / 3600}小时前", "type": "string_literal"}, {"context": "diff < 86400 * 2 -> \"昨天\"", "line": 100, "text": "昨天", "type": "string_literal"}, {"context": "diff < 86400 * 7 -> \"${diff / 86400}天前\"", "line": 101, "text": "${diff / 86400}天前", "type": "string_literal"}, {"context": "hours > 0 -> String.format(\"%d小时%d分钟%d秒\", hours, minutes, remainingSeconds)", "line": 197, "text": "%d小时%d分钟%d秒", "type": "string_literal"}, {"context": "minutes > 0 -> String.format(\"%d分钟%d秒\", minutes, remainingSeconds)", "line": 198, "text": "%d分钟%d秒", "type": "string_literal"}, {"context": "else -> String.format(\"%d秒\", remainingSeconds)", "line": 199, "text": "%d秒", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/utils/FileUtils.kt": [{"context": "throw IOException(\"无法创建录音目录: ${dir.absolutePath}\")", "line": 113, "text": "无法创建录音目录: ${dir.absolutePath}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/utils/LogUtils.kt": [{"context": "d(\"JSON空字符串\", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)", "line": 212, "text": "JSON空字符串", "type": "string_literal"}, {"context": "e(e, \"JSON格式化失败: $json\", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)", "line": 220, "text": "JSON格式化失败: $json", "type": "string_literal"}, {"context": "eTag(tag, e, \"JSON格式化失败: $json\", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)", "line": 238, "text": "JSON格式化失败: $json", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/utils/PermissionUtils.kt": [{"context": "Manifest.permission.RECORD_AUDIO -> \"录音权限：用于录制语音内容\"", "line": 165, "text": "录音权限：用于录制语音内容", "type": "string_literal"}, {"context": "Manifest.permission.READ_EXTERNAL_STORAGE -> \"存储读取权限：用于读取音频文件\"", "line": 166, "text": "存储读取权限：用于读取音频文件", "type": "string_literal"}, {"context": "Manifest.permission.WRITE_EXTERNAL_STORAGE -> \"存储写入权限：用于保存录音文件\"", "line": 167, "text": "存储写入权限：用于保存录音文件", "type": "string_literal"}, {"context": "Manifest.permission.READ_MEDIA_AUDIO -> \"媒体音频权限：用于访问音频文件\"", "line": 168, "text": "媒体音频权限：用于访问音频文件", "type": "string_literal"}, {"context": "else -> \"未知权限：$permission\"", "line": 169, "text": "未知权限：$permission", "type": "string_literal"}, {"context": "\"\"\"多行字符串 (第176行)\"\"\"", "line": 176, "text": "录音功能需要以下权限：\n        \n        • 录音权限：用于录制语音内容\n        • 存储权限：用于保存和读取录音文件\n        \n        请授予这些权限以正常使用录音功能。", "type": "multiline_string"}], "app/src/main/java/com/buque/wakoo/utils/datapoints/ShushuUtils.kt": [{"context": "newProperties.put(\"gender\", if (it.gender == 1) \"男\" else \"女\")", "line": 103, "text": "男", "type": "string_literal"}, {"context": "newProperties.put(\"gender\", if (it.gender == 1) \"男\" else \"女\")", "line": 103, "text": "女", "type": "string_literal"}, {"context": "\"gender\" to if (it.gender == 1) \"男\" else \"女\",", "line": 118, "text": "男", "type": "string_literal"}, {"context": "\"gender\" to if (it.gender == 1) \"男\" else \"女\",", "line": 118, "text": "女", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/utils/koru/sample.kt": [{"context": "is BrokerEvent.TaskSubmitted -> \"✅ [Broker] 任务已提交: ID=${event.taskId}, 优先级=${event.task.priority}\"", "line": 24, "text": "✅ [Broker] 任务已提交: ID=${event.taskId}, 优先级=${event.task.priority}", "type": "string_literal"}, {"context": "is BrokerEvent.TaskRetracted -> \"↩️ [Broker] 任务已撤回: ID=${event.taskId}\"", "line": 25, "text": "↩️ [Broker] 任务已撤回: ID=${event.taskId}", "type": "string_literal"}, {"context": "is BrokerEvent.TaskPriorityUpdated -> \"🔼 [Broker] 优先级更新: ID=${event.taskId}, 新优先级=${event.newPriority}\"", "line": 26, "text": "🔼 [Broker] 优先级更新: ID=${event.taskId}, 新优先级=${event.newPriority}", "type": "string_literal"}, {"context": "is BrokerEvent.TaskDispatched -> \"🚚 [Broker] 任务已分发: ID=${event.taskId} -> Consumer [${event.consumerId}]\"", "line": 27, "text": "🚚 [Broker] 任务已分发: ID=${event.taskId} -> Consumer [${event.consumerId}]", "type": "string_literal"}, {"context": "is BrokerEvent.TaskExecutionStarted -> \"▶️ [Consumer] [${event.consumerId}] 开始执行任务: ID=${event.taskId}\"", "line": 28, "text": "▶️ [Consumer] [${event.consumerId}] 开始执行任务: ID=${event.taskId}", "type": "string_literal"}, {"context": "is BrokerEvent.TaskExecutionSuccess -> \"✔️ [Consumer] [${event.consumerId}] 成功完成任务: ID=${event.taskId}\"", "line": 29, "text": "✔️ [Consumer] [${event.consumerId}] 成功完成任务: ID=${event.taskId}", "type": "string_literal"}, {"context": "is BrokerEvent.TaskExecutionFailed -> \"❌ [Consumer] [${event.consumerId}] 任务失败: ID=${event.taskId}, 原因: ${event.error.message}\"", "line": 30, "text": "❌ [Consumer] [${event.consumerId}] 任务失败: ID=${event.taskId}, 原因: ${event.error.message}", "type": "string_literal"}, {"context": "is BrokerEvent.TaskExecutionCancelled -> \"❌ [Consumer] [${event.consumerId}] 任务取消: ID=${event.taskId}\"", "line": 31, "text": "❌ [Consumer] [${event.consumerId}] 任务取消: ID=${event.taskId}", "type": "string_literal"}, {"context": "is BrokerEvent.QueueCleared -> \"🗑️ [Broker] 任务队列已清空\"", "line": 32, "text": "🗑️ [Broker] 任务队列已清空", "type": "string_literal"}, {"context": "is BrokerEvent.BrokerShutdown -> \"🛑 [Broker] 调度中心已关闭\"", "line": 33, "text": "🛑 [Broker] 调度中心已关闭", "type": "string_literal"}, {"context": "broker.submit(createTask(\"普通邮件\", 5, 1000))", "line": 49, "text": "普通邮件", "type": "string_literal"}, {"context": "broker.submit(createTask(\"报告生成\", 2, 2000))", "line": 50, "text": "报告生成", "type": "string_literal"}, {"context": "broker.submit(createTask(\"!!!紧急修复!!!\", 10, 1500))", "line": 51, "text": "!!!紧急修复!!!", "type": "string_literal"}, {"context": "PayloadTask(id = lowPriorityTaskId, \"低优先级任务\", 1, onAction = { payload ->", "line": 57, "text": "低优先级任务", "type": "string_literal"}, {"context": "broker.submit(createTask(\"另一个普通任务\", 5, 1000))", "line": 62, "text": "另一个普通任务", "type": "string_literal"}, {"context": "broker.submit(createTask(\"会被消费的任务\", 5, 1000))", "line": 70, "text": "会被消费的任务", "type": "string_literal"}, {"context": "\"这个任务将被撤回\",", "line": 74, "text": "这个任务将被撤回", "type": "string_literal"}, {"context": "broker.submit(createTask(\"任务A-1 (给A)\", 7, 1000))", "line": 86, "text": "任务A-1 (给A)", "type": "string_literal"}, {"context": "broker.submit(createTask(\"任务C-1 (给B)\", 8, 1000))", "line": 87, "text": "任务C-1 (给B)", "type": "string_literal"}, {"context": "PayloadTask<String>(payload = \"会失败的任务\", priority = 9, onError = { e ->", "line": 95, "text": "会失败的任务", "type": "string_literal"}, {"context": "}) { throw IllegalStateException(\"数据库连接失败\") }", "line": 97, "text": "数据库连接失败", "type": "string_literal"}, {"context": "val longRunningTask = createTask(\"一个很长的任务\", 8, 10000)", "line": 100, "text": "一个很长的任务", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/utils/ninepatch/NinePathLoader.kt": [{"context": "LogUtils.i(\"DotNinePng\", \"提前预加载点九bitmap: ${cacheSet.size}\")", "line": 46, "text": "提前预加载点九bitmap: ${cacheSet.size}", "type": "string_literal"}], "app/src/main/java/com/buque/wakoo/viewmodel/GiftWallDetailViewModel.kt": [{"context": "return Result.failure(Exception(\"无法施舍礼物,因为begId = null\"))", "line": 132, "text": "无法施舍礼物,因为begId = null", "type": "string_literal"}]}, "summary": {"scan_directories": ["app/src/main/java", "lib-webview/src"], "total_files": 125, "total_strings": 702}}