# 生产环境UI中文字符串报告
==================================================

## 扫描说明
本报告专注于识别生产环境中真正需要本地化的UI文本

### 已排除的内容：
- 🚫 预览函数 (@Preview 注解的函数)
- 🚫 日志语句 (Log.*, LogUtils.*, println等)
- 🚫 测试和调试相关代码
- 🚫 数据模型中的示例数据
- 🚫 注释内容

扫描目录: app/src/main/java
总文件数: 13
总字符串数: 23
🔴 高置信度: 23
🟡 中等置信度: 0

## app/src/main/java/com/buque/wakoo/bean/RechargeData.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 78 行**:
```kotlin
val description: String, // 描述，如"充值钻石"
```
中文内容: `充值钻石`

----------------------------------------

## app/src/main/java/com/buque/wakoo/manager/AudioRecordManager.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 729 行**:
```kotlin
"释放录制器失败",
```
中文内容: `释放录制器失败`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/dressup/SilverShopListScreen.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 691 行**:
```kotlin
text = "总计需要消耗银币：%s\n兑换成功后将发放至礼物背包".localizedFormatWithKey("总计需要消耗银币", totalPrice),
```
中文内容: `总计需要消耗银币`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/japan/boost/SignContent.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 110 行**:
```kotlin
contentDescription = "关闭",
```
中文内容: `关闭`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/login/LoginHostScreen.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 60 行**:
```kotlin
showToast("该账号未注册, 切换失败")
```
中文内容: `该账号未注册, 切换失败`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/login/LoginScreen.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 132 行**:
```kotlin
text = "账号管理",
```
中文内容: `账号管理`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/messages/notification/NotificationPage.kt
发现 10 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 55 行**:
```kotlin
message = "我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...",
```
中文内容: `我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...`

**第 64 行**:
```kotlin
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 72 行**:
```kotlin
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 79 行**:
```kotlin
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 86 行**:
```kotlin
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 99 行**:
```kotlin
message = "系统通知：您的账号已激活！",
```
中文内容: `系统通知：您的账号已激活！`

**第 107 行**:
```kotlin
message = "关注了你",
```
中文内容: `关注了你`

**第 114 行**:
```kotlin
message = "喜欢了你的作品",
```
中文内容: `喜欢了你的作品`

**第 159 行**:
```kotlin
title = "私信",
```
中文内容: `私信`

**第 169 行**:
```kotlin
title = "通知",
```
中文内容: `通知`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/profile/ProfileVoiceEditScreen.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 218 行**:
```kotlin
message = "删除文件失败",
```
中文内容: `删除文件失败`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/recharge/RechargeRecordScreen.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 142 行**:
```kotlin
"充值",
```
中文内容: `充值`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/voice/VoicePublishScreen.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 323 行**:
```kotlin
message = "删除文件失败",
```
中文内容: `删除文件失败`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/BasicUI.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 325 行**:
```kotlin
contentDescription = "返回",
```
中文内容: `返回`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/LogUtils.kt
发现 2 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 220 行**:
```kotlin
e(e, "JSON格式化失败: $json", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)
```
中文内容: `JSON格式化失败: $json`

**第 238 行**:
```kotlin
eTag(tag, e, "JSON格式化失败: $json", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)
```
中文内容: `JSON格式化失败: $json`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/PermissionUtils.kt
发现 1 个生产环境UI字符串:

### 🔴 高置信度 (强烈建议本地化)

**第 167 行**:
```kotlin
Manifest.permission.WRITE_EXTERNAL_STORAGE -> "存储写入权限：用于保存录音文件"
```
中文内容: `存储写入权限：用于保存录音文件`

----------------------------------------

