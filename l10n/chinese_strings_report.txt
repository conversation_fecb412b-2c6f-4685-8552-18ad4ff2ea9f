# 包含中文但没有本地化的字符串报告
==================================================

扫描目录: app/src/main/java, lib-webview/src
总文件数: 125
总字符串数: 702

## app/src/main/java/com/buque/wakoo/bean/Dressup.kt
发现 2 个未本地化的中文字符串:

**第 193 行** (string_literal):
```
if (expireTimestamp != null) "${DateTimeUtils.formatDate(expireTimestamp!!, "yyyy-MM-dd")}到期" else "永久期限"
```
中文内容: `)}到期`

**第 193 行** (string_literal):
```
if (expireTimestamp != null) "${DateTimeUtils.formatDate(expireTimestamp!!, "yyyy-MM-dd")}到期" else "永久期限"
```
中文内容: `永久期限`

----------------------------------------

## app/src/main/java/com/buque/wakoo/bean/Gift.kt
发现 5 个未本地化的中文字符串:

**第 216 行** (string_literal):
```
"name": "空中花园",
```
中文内容: `空中花园`

**第 224 行** (string_literal):
```
"action_btn_txt": "赠送/求打赏/打赏礼物",
```
中文内容: `赠送/求打赏/打赏礼物`

**第 226 行** (string_literal):
```
"cant_action_hint": "该礼物已下线", # can_give为False时，按钮点击提示，可能为"", 为""则点击无反应
```
中文内容: `该礼物已下线`

**第 226 行** (string_literal):
```
"cant_action_hint": "该礼物已下线", # can_give为False时，按钮点击提示，可能为"", 为""则点击无反应
```
中文内容: `, 为`

**第 227 行** (string_literal):
```
"hint": "背包礼物可赠送",
```
中文内容: `背包礼物可赠送`

----------------------------------------

## app/src/main/java/com/buque/wakoo/bean/LiveRoom.kt
发现 3 个未本地化的中文字符串:

**第 81 行** (string_literal):
```
title = "你要约会吗?",
```
中文内容: `你要约会吗?`

**第 85 行** (string_literal):
```
desc = "大家来约会吧",
```
中文内容: `大家来约会吧`

**第 86 行** (string_literal):
```
notice = "想要上麦请扣1",
```
中文内容: `想要上麦请扣1`

----------------------------------------

## app/src/main/java/com/buque/wakoo/bean/PrivilegedGiftRemindBean.kt
发现 4 个未本地化的中文字符串:

**第 18 行** (string_literal):
```
"nickname": "诗书塞外",
```
中文内容: `诗书塞外`

**第 34 行** (string_literal):
```
"desc": "你还有背包礼物道具未使用\n送个礼物给她吧，她会很开心哦",
```
中文内容: `你还有背包礼物道具未使用\n送个礼物给她吧，她会很开心哦`

**第 37 行** (string_literal):
```
"name": "情人礼盒",
```
中文内容: `情人礼盒`

**第 40 行** (string_literal):
```
"btn_txt": "免费赠送"
```
中文内容: `免费赠送`

----------------------------------------

## app/src/main/java/com/buque/wakoo/bean/VoiceData.kt
发现 21 个未本地化的中文字符串:

**第 55 行** (string_literal):
```
title = "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話",
```
中文内容: `有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話`

**第 60 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 61 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 62 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 63 行** (string_literal):
```
VoiceTag(1, "哈哈a"),
```
中文内容: `哈哈a`

**第 64 行** (string_literal):
```
VoiceTag(1, "哈哈哈"),
```
中文内容: `哈哈哈`

**第 65 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 66 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 67 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 68 行** (string_literal):
```
VoiceTag(1, "哈哈哈"),
```
中文内容: `哈哈哈`

**第 151 行** (string_literal):
```
title = "有時話不多",
```
中文内容: `有時話不多`

**第 152 行** (string_literal):
```
desc = "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話",
```
中文内容: `有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話`

**第 156 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 157 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 158 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 159 行** (string_literal):
```
VoiceTag(1, "哈哈a"),
```
中文内容: `哈哈a`

**第 160 行** (string_literal):
```
VoiceTag(1, "哈哈哈"),
```
中文内容: `哈哈哈`

**第 161 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 162 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 163 行** (string_literal):
```
VoiceTag(1, "哈哈"),
```
中文内容: `哈哈`

**第 164 行** (string_literal):
```
VoiceTag(1, "哈哈哈"),
```
中文内容: `哈哈哈`

----------------------------------------

## app/src/main/java/com/buque/wakoo/bean/user/BasicUser.kt
发现 2 个未本地化的中文字符串:

**第 92 行** (string_literal):
```
name = "小帅",
```
中文内容: `小帅`

**第 102 行** (string_literal):
```
"幼儿园班花",
```
中文内容: `幼儿园班花`

----------------------------------------

## app/src/main/java/com/buque/wakoo/consts/Pay.kt
发现 13 个未本地化的中文字符串:

**第 19 行** (string_literal):
```
*     (1, "GOOGLE_IAP", "Google应用内购买"),
```
中文内容: `Google应用内购买`

**第 20 行** (string_literal):
```
*     (2, "APPLE_IAP", "Apple应用内购买"),
```
中文内容: `Apple应用内购买`

**第 21 行** (string_literal):
```
*     (3, "WXPAY", "微信支付"),
```
中文内容: `微信支付`

**第 22 行** (string_literal):
```
*     (4, "ALIPAY", "支付宝"),
```
中文内容: `支付宝`

**第 24 行** (string_literal):
```
*     (6, "JKOPAY", "街口支付"),  # 目前只支持台湾
```
中文内容: `街口支付`

**第 58 行** (string_literal):
```
*     (1, "GOOGLE_SDK", "谷歌SDK"),
```
中文内容: `谷歌SDK`

**第 59 行** (string_literal):
```
*     (2, "APPLE_SDK", "苹果SDK"),
```
中文内容: `苹果SDK`

**第 60 行** (string_literal):
```
*     (3, "WXSDK", "微信SDK"),
```
中文内容: `微信SDK`

**第 61 行** (string_literal):
```
*     (4, "LINK_WEB", "浏览器打开链接"),
```
中文内容: `浏览器打开链接`

**第 62 行** (string_literal):
```
*     (5, "LINK_WEB1", "浏览器打开链接(适配客户端)"),
```
中文内容: `浏览器打开链接(适配客户端)`

**第 63 行** (string_literal):
```
*     (6, "LINK_WEBVIEW", "webview打开链接"),
```
中文内容: `webview打开链接`

**第 64 行** (string_literal):
```
*     (7, "ORDER_LINK", "获取订单链接"),
```
中文内容: `获取订单链接`

**第 65 行** (string_literal):
```
*     (8, "AGENT", "代充"),
```
中文内容: `代充`

----------------------------------------

## app/src/main/java/com/buque/wakoo/consts/SceneType.kt
发现 6 个未本地化的中文字符串:

**第 6 行** (string_literal):
```
* (1, "TRIBE", "部落"),
```
中文内容: `部落`

**第 7 行** (string_literal):
```
* (2, "AUDIOROOM", "语音房"),
```
中文内容: `语音房`

**第 8 行** (string_literal):
```
* (3, "RPIVATEROOM", "私密小屋")
```
中文内容: `私密小屋`

**第 9 行** (string_literal):
```
* (4, "RPIVATE_CHAT", "私聊"),
```
中文内容: `私聊`

**第 10 行** (string_literal):
```
* (5, "MOMENT", "动态"),
```
中文内容: `动态`

**第 11 行** (string_literal):
```
* (6, "PROFILE", "个人主页"),
```
中文内容: `个人主页`

----------------------------------------

## app/src/main/java/com/buque/wakoo/core/pay/AppPayCoreKit.kt
发现 1 个未本地化的中文字符串:

**第 157 行** (string_literal):
```
showToast("创建订单失败，productId: $productId, orderType: ${request.orderType}")
```
中文内容: `创建订单失败，productId: $productId, orderType: ${request.orderType}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/core/pay/GoogleBillingManager.kt
发现 4 个未本地化的中文字符串:

**第 158 行** (string_literal):
```
logI("google支付连接成功")
```
中文内容: `google支付连接成功`

**第 163 行** (string_literal):
```
reportBillingError("google支付服务不可用，code: ${billingResult.responseCode}")
```
中文内容: `google支付服务不可用，code: ${billingResult.responseCode}`

**第 168 行** (string_literal):
```
reportBillingError("google支付服务连接出错，code: ${billingResult.responseCode}")
```
中文内容: `google支付服务连接出错，code: ${billingResult.responseCode}`

**第 267 行** (string_literal):
```
LogUtils.e("ERR:不支持PRODUCT_DETAILS")
```
中文内容: `ERR:不支持PRODUCT_DETAILS`

----------------------------------------

## app/src/main/java/com/buque/wakoo/core/webview/AppLinkNavigator.kt
发现 1 个未本地化的中文字符串:

**第 73 行** (string_literal):
```
throw IllegalAccessError("${rc.simpleName} 类未找到 companion.fromUri Method")
```
中文内容: `${rc.simpleName} 类未找到 companion.fromUri Method`

----------------------------------------

## app/src/main/java/com/buque/wakoo/core/webview/offline/OfflinePkgManager.kt
发现 6 个未本地化的中文字符串:

**第 250 行** (string_literal):
```
logI(TAG, "下载游戏成功[${game.name}]")
```
中文内容: `下载游戏成功[${game.name}]`

**第 252 行** (string_literal):
```
logI(TAG, "开始解压游戏[${game.name}]")
```
中文内容: `开始解压游戏[${game.name}]`

**第 261 行** (string_literal):
```
"解压游戏成功[${game.name}]=>文件夹${destGameZip.parentFile?.absolutePath}",
```
中文内容: `解压游戏成功[${game.name}]=>文件夹${destGameZip.parentFile?.absolutePath}`

**第 271 行** (string_literal):
```
logI(TAG, "游戏配置写入成功[${game.name}]：$game")
```
中文内容: `游戏配置写入成功[${game.name}]：$game`

**第 275 行** (string_literal):
```
"下载游戏失败[${game.name}]：" + result.exceptionOrNull()?.message.orEmpty(),
```
中文内容: `下载游戏失败[${game.name}]：`

**第 280 行** (string_literal):
```
logE(TAG, "下载游戏失败：" + e.message.orEmpty())
```
中文内容: `下载游戏失败：`

----------------------------------------

## app/src/main/java/com/buque/wakoo/game/core/Attributes.kt
发现 12 个未本地化的中文字符串:

**第 22 行** (string_literal):
```
override val name: String = "重力属性"
```
中文内容: `重力属性`

**第 23 行** (string_literal):
```
override val desc: String = "脚下必须有支撑点才不会下落"
```
中文内容: `脚下必须有支撑点才不会下落`

**第 31 行** (string_literal):
```
override val name: String = "可食用属性"
```
中文内容: `可食用属性`

**第 32 行** (string_literal):
```
override val desc: String = "可以被小蛇吃掉，改变蛇的长度"
```
中文内容: `可以被小蛇吃掉，改变蛇的长度`

**第 38 行** (string_literal):
```
override val name: String = "致命属性"
```
中文内容: `致命属性`

**第 39 行** (string_literal):
```
override val desc: String = "接触后小蛇死亡，游戏结束"
```
中文内容: `接触后小蛇死亡，游戏结束`

**第 45 行** (string_literal):
```
override val name: String = "支撑点属性"
```
中文内容: `支撑点属性`

**第 46 行** (string_literal):
```
override val desc: String = "可以支撑有重力属性的元素不下落"
```
中文内容: `可以支撑有重力属性的元素不下落`

**第 52 行** (string_literal):
```
override val name: String = "可推动属性"
```
中文内容: `可推动属性`

**第 53 行** (string_literal):
```
override val desc: String = "可以被其他可移动元素推动"
```
中文内容: `可以被其他可移动元素推动`

**第 59 行** (string_literal):
```
override val name: String = "胜利属性"
```
中文内容: `胜利属性`

**第 60 行** (string_literal):
```
override val desc: String = "小蛇头部接触后通关"
```
中文内容: `小蛇头部接触后通关`

----------------------------------------

## app/src/main/java/com/buque/wakoo/game/core/Elements.kt
发现 14 个未本地化的中文字符串:

**第 39 行** (string_literal):
```
name = "空格",
```
中文内容: `空格`

**第 40 行** (string_literal):
```
desc = "空的地图格子，可以被其他元素占据",
```
中文内容: `空的地图格子，可以被其他元素占据`

**第 48 行** (string_literal):
```
name = "墙壁",
```
中文内容: `墙壁`

**第 49 行** (string_literal):
```
desc = "固定的障碍物，提供支撑",
```
中文内容: `固定的障碍物，提供支撑`

**第 57 行** (string_literal):
```
name = "苹果",
```
中文内容: `苹果`

**第 58 行** (string_literal):
```
desc = "普通食物，小蛇吃掉后长度+1",
```
中文内容: `普通食物，小蛇吃掉后长度+1`

**第 66 行** (string_literal):
```
name = "毒刺",
```
中文内容: `毒刺`

**第 67 行** (string_literal):
```
desc = "危险的尖刺，接触后小蛇死亡",
```
中文内容: `危险的尖刺，接触后小蛇死亡`

**第 75 行** (string_literal):
```
name = "石头",
```
中文内容: `石头`

**第 76 行** (string_literal):
```
desc = "可推动的石头，有重力会下落",
```
中文内容: `可推动的石头，有重力会下落`

**第 84 行** (string_literal):
```
name = "出口",
```
中文内容: `出口`

**第 85 行** (string_literal):
```
desc = "关卡出口，小蛇到达后通关",
```
中文内容: `关卡出口，小蛇到达后通关`

**第 102 行** (string_literal):
```
override val name: String = "蛇身",
```
中文内容: `蛇身`

**第 103 行** (string_literal):
```
override val desc: String = "小蛇的身体部分",
```
中文内容: `小蛇的身体部分`

----------------------------------------

## app/src/main/java/com/buque/wakoo/game/core/LevelMap.kt
发现 7 个未本地化的中文字符串:

**第 64 行** (string_literal):
```
throw IllegalArgumentException("请使用placeSnakeHead或placeSnakeBody方法添加小蛇")
```
中文内容: `请使用placeSnakeHead或placeSnakeBody方法添加小蛇`

**第 86 行** (string_literal):
```
throw IllegalStateException("只能从蛇尾开始移除")
```
中文内容: `只能从蛇尾开始移除`

**第 105 行** (string_literal):
```
throw IllegalStateException("只能设置一个蛇头")
```
中文内容: `只能设置一个蛇头`

**第 119 行** (string_literal):
```
throw IllegalStateException("请先设置蛇头")
```
中文内容: `请先设置蛇头`

**第 136 行** (string_literal):
```
throw IllegalArgumentException("地图尺寸不能小于1")
```
中文内容: `地图尺寸不能小于1`

**第 206 行** (string_literal):
```
throw IllegalArgumentException("起始位置不能大于结束位置")
```
中文内容: `起始位置不能大于结束位置`

**第 220 行** (string_literal):
```
throw IndexOutOfBoundsException("平移后区域超出地图边界")
```
中文内容: `平移后区域超出地图边界`

----------------------------------------

## app/src/main/java/com/buque/wakoo/game/core/Snake.kt
发现 5 个未本地化的中文字符串:

**第 43 行** (string_literal):
```
"蛇太短了，至少要有一节身体"
```
中文内容: `蛇太短了，至少要有一节身体`

**第 50 行** (string_literal):
```
check(uniquePositions.size == segments.size) { "蛇的身体出现重叠" }
```
中文内容: `蛇的身体出现重叠`

**第 59 行** (string_literal):
```
check(isConnected) { "蛇的身体断开了，每一节都要连在一起" }
```
中文内容: `蛇的身体断开了，每一节都要连在一起`

**第 66 行** (string_literal):
```
check(validHead) { "蛇头的位置不对，找不到一条可以走完全身的路径" }
```
中文内容: `蛇头的位置不对，找不到一条可以走完全身的路径`

**第 80 行** (string_literal):
```
check(!invalidDirection) { "蛇头正对着自己的身体，这样会撞到自己" }
```
中文内容: `蛇头正对着自己的身体，这样会撞到自己`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im/UCInstanceMessage.kt
发现 1 个未本地化的中文字符串:

**第 29 行** (string_literal):
```
return "(已撤回)${getSummaryString()}"
```
中文内容: `(已撤回)${getSummaryString()}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im/api/IMApi.kt
发现 4 个未本地化的中文字符串:

**第 20 行** (string_literal):
```
*   "toast": "陌生人之间最多只能发送15条消息哦",  # toast 提示，为空字符串则不 toast
```
中文内容: `陌生人之间最多只能发送15条消息哦`

**第 22 行** (string_literal):
```
*   "hint": "陌生人之间最多发送15条消息\n互相关注或者添加对方为好友后聊天不限制",  # trigger_block_card 为 True 时为拦截卡片文案
```
中文内容: `陌生人之间最多发送15条消息\n互相关注或者添加对方为好友后聊天不限制`

**第 23 行** (string_literal):
```
*   "block_card_btn_txt": "添加对方为好友"  # trigger_block_card 为 True 时为拦截卡片按钮文案
```
中文内容: `添加对方为好友`

**第 28 行** (string_literal):
```
*   "hint": "您的免费消息条数已用完，接下来发消息{}钻石/条\n和对方互相关注成为好友后，可无限制聊天"
```
中文内容: `您的免费消息条数已用完，接下来发消息{}钻石/条\n和对方互相关注成为好友后，可无限制聊天`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/GlobalNotification.kt
发现 2 个未本地化的中文字符串:

**第 482 行** (string_literal):
```
name = "哈哈哈大地方",
```
中文内容: `哈哈哈大地方`

**第 485 行** (string_literal):
```
desc = buildAnnotatedString { append("哈哈哈大地方") },
```
中文内容: `哈哈哈大地方`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/conversation/AppConversationManger.kt
发现 2 个未本地化的中文字符串:

**第 132 行** (string_literal):
```
IMLogUtils.i("开始加载会话列表")
```
中文内容: `开始加载会话列表`

**第 382 行** (string_literal):
```
LogUtils.d("IMCompatCore", "未知类型的会话: ${it.id}")
```
中文内容: `未知类型的会话: ${it.id}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/types/UCCustomMessage.kt
发现 1 个未本地化的中文字符串:

**第 68 行** (string_literal):
```
override fun getSummaryString(): String = "自定义消息, cmd: $cmd, summary: $summary"
```
中文内容: `自定义消息, cmd: $cmd, summary: $summary`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/types/UCEmojiMessage.kt
发现 5 个未本地化的中文字符串:

**第 26 行** (string_literal):
```
TYPE_DICE -> "骰子"
```
中文内容: `骰子`

**第 27 行** (string_literal):
```
in TYPE_NUMBER_1..TYPE_NUMBER_5 -> "幸运数字"
```
中文内容: `幸运数字`

**第 28 行** (string_literal):
```
TYPE_GUESSING_FIST -> "猜拳"
```
中文内容: `猜拳`

**第 29 行** (string_literal):
```
else -> "未知"
```
中文内容: `未知`

**第 31 行** (string_literal):
```
return "表情消息(${name}), type: $type, name: $value"
```
中文内容: `表情消息(${name}), type: $type, name: $value`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/types/UCFakeMessage.kt
发现 2 个未本地化的中文字符串:

**第 33 行** (string_literal):
```
override fun getSummaryString(): String = "时间戳：${getMessageTimeFormatText(WakooApplication.instance)}"
```
中文内容: `时间戳：${getMessageTimeFormatText(WakooApplication.instance)}`

**第 63 行** (string_literal):
```
override fun getSummaryString(): String = "以下为新消息"
```
中文内容: `以下为新消息`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/types/UCGiftMessage.kt
发现 1 个未本地化的中文字符串:

**第 19 行** (string_literal):
```
override fun getSummaryString(): String = "礼物消息, id: ${gift.gift.id}, 名称: ${gift.gift.name}"
```
中文内容: `礼物消息, id: ${gift.gift.id}, 名称: ${gift.gift.name}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/types/UCImageMessage.kt
发现 1 个未本地化的中文字符串:

**第 33 行** (string_literal):
```
return "图片消息, 宽: ${elem?.width}, 高: ${elem?.height}, url: ${elem?.source}"
```
中文内容: `图片消息, 宽: ${elem?.width}, 高: ${elem?.height}, url: ${elem?.source}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/types/UCMicEmojiMessage.kt
发现 3 个未本地化的中文字符串:

**第 31 行** (string_literal):
```
TYPE_WEBP -> "普通动态表情"
```
中文内容: `普通动态表情`

**第 32 行** (string_literal):
```
TYPE_WEBP_AND_RESULT -> "带结果的动态表情"
```
中文内容: `带结果的动态表情`

**第 35 行** (string_literal):
```
return "表情消息(${content.emojiEffect.name}), type: $type"
```
中文内容: `表情消息(${content.emojiEffect.name}), type: $type`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/types/UCTextMessage.kt
发现 1 个未本地化的中文字符串:

**第 21 行** (string_literal):
```
override fun getSummaryString(): String = "文本消息：$text"
```
中文内容: `文本消息：$text`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/types/UCUnknownMessage.kt
发现 1 个未本地化的中文字符串:

**第 12 行** (string_literal):
```
return "未知消息类型：${base}"
```
中文内容: `未知消息类型：${base}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/types/UCVoiceMessage.kt
发现 1 个未本地化的中文字符串:

**第 16 行** (string_literal):
```
override fun getSummaryString(): String = "语音消息, 时长: $duration, url: $url"
```
中文内容: `语音消息, 时长: $duration, url: $url`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/ui/custom/CommonTextButtonContent.kt
发现 1 个未本地化的中文字符串:

**第 72 行** (string_literal):
```
"哈哈哈哈哈",
```
中文内容: `哈哈哈哈哈`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/ui/custom/MsgInterceptContent.kt
发现 1 个未本地化的中文字符串:

**第 125 行** (string_literal):
```
"这是测试用的消息拦截消息展示",
```
中文内容: `这是测试用的消息拦截消息展示`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/ui/custom/SendGiftMessageItem.kt
发现 5 个未本地化的中文字符串:

**第 138 行** (string_literal):
```
val g = GiftWall.GiftWrapper.Gift(name = "礼物名称", price = 5200)
```
中文内容: `礼物名称`

**第 141 行** (string_literal):
```
message = "你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦",
```
中文内容: `你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦`

**第 142 行** (string_literal):
```
buttonText = "免费赠送",
```
中文内容: `免费赠送`

**第 147 行** (string_literal):
```
message = "你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦",
```
中文内容: `你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦`

**第 148 行** (string_literal):
```
buttonText = "已赠送",
```
中文内容: `已赠送`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/ui/entry/GiftMsgEntry.kt
发现 4 个未本地化的中文字符串:

**第 167 行** (string_literal):
```
"赠送福袋获得奖励",
```
中文内容: `赠送福袋获得奖励`

**第 177 行** (string_literal):
```
"赠送多人福袋获得奖励",
```
中文内容: `赠送多人福袋获得奖励`

**第 190 行** (string_literal):
```
"赠送福袋没有获得奖励",
```
中文内容: `赠送福袋没有获得奖励`

**第 199 行** (string_literal):
```
"赠送多人福袋没有获得奖励",
```
中文内容: `赠送多人福袋没有获得奖励`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/ui/entry/PrivateRoomEventEntry.kt
发现 2 个未本地化的中文字符串:

**第 105 行** (string_literal):
```
"幼儿园搬花已经进入了私密小屋",
```
中文内容: `幼儿园搬花已经进入了私密小屋`

**第 106 行** (string_literal):
```
"加入互动",
```
中文内容: `加入互动`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/message/ui/entry/VoiceCallEventEntry.kt
发现 2 个未本地化的中文字符串:

**第 79 行** (string_literal):
```
"对方挂断了语音,本次通话已结束点击下方按钮可继续聊天",
```
中文内容: `对方挂断了语音,本次通话已结束点击下方按钮可继续聊天`

**第 80 行** (string_literal):
```
"继续聊天",
```
中文内容: `继续聊天`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/rtm/RtmMsgHandler.kt
发现 2 个未本地化的中文字符串:

**第 256 行** (string_literal):
```
"飘屏赠送福袋获得奖励",
```
中文内容: `飘屏赠送福袋获得奖励`

**第 267 行** (string_literal):
```
"飘屏赠送多人福袋获得奖励",
```
中文内容: `飘屏赠送多人福袋获得奖励`

----------------------------------------

## app/src/main/java/com/buque/wakoo/im_business/tim/TIMEngine.kt
发现 6 个未本地化的中文字符串:

**第 1172 行** (string_literal):
```
IMLogUtils.i("重发消息成功 => $rawMessage")
```
中文内容: `重发消息成功 => $rawMessage`

**第 1174 行** (string_literal):
```
IMLogUtils.i("发送消息成功 => $rawMessage")
```
中文内容: `发送消息成功 => $rawMessage`

**第 1202 行** (string_literal):
```
IMLogUtils.i("重发消息失败code$code => desc:$desc => $rawMessage")
```
中文内容: `重发消息失败code$code => desc:$desc => $rawMessage`

**第 1204 行** (string_literal):
```
IMLogUtils.i("发送消息失败code$code => desc:$desc => $rawMessage")
```
中文内容: `发送消息失败code$code => desc:$desc => $rawMessage`

**第 1261 行** (string_literal):
```
IMLogUtils.w("当前的user = null, 不能转换成IMUser")
```
中文内容: `当前的user = null, 不能转换成IMUser`

**第 1344 行** (string_literal):
```
IMLogUtils.i("重发消息通知:$ucMessage")
```
中文内容: `重发消息通知:$ucMessage`

----------------------------------------

## app/src/main/java/com/buque/wakoo/manager/AudioPlayerManager.kt
发现 23 个未本地化的中文字符串:

**第 165 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.play() 开始播放音频, tag=$tag, uri=$uri")
```
中文内容: `AudioPlayerManager.play() 开始播放音频, tag=$tag, uri=$uri`

**第 179 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.onPrepared() 音频准备完成, tag=$tag, duration=${audioDuration}ms")
```
中文内容: `AudioPlayerManager.onPrepared() 音频准备完成, tag=$tag, duration=${audioDuration}ms`

**第 195 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.onPrepared() 开始播放并更新状态, tag=$tag")
```
中文内容: `AudioPlayerManager.onPrepared() 开始播放并更新状态, tag=$tag`

**第 200 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.onCompletion() 播放完成, tag=$tag")
```
中文内容: `AudioPlayerManager.onCompletion() 播放完成, tag=$tag`

**第 208 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.onSeekComplete() 跳转完成, tag=$tag, position=${accumulatedTimeMs}ms")
```
中文内容: `AudioPlayerManager.onSeekComplete() 跳转完成, tag=$tag, position=${accumulatedTimeMs}ms`

**第 217 行** (string_literal):
```
LogUtils.e("AudioPlayerManager.onError() 播放出错, tag=$tag, what=$what, extra=$extra")
```
中文内容: `AudioPlayerManager.onError() 播放出错, tag=$tag, what=$what, extra=$extra`

**第 233 行** (string_literal):
```
LogUtils.e(e, "AudioPlayerManager.play() 播放初始化失败, tag=$tag")
```
中文内容: `AudioPlayerManager.play() 播放初始化失败, tag=$tag`

**第 249 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.pause() 暂停播放, tag=${currentState.currentlyPlayingTag}, isPlaying=${mediaPlayer?.isPlaying}")
```
中文内容: `AudioPlayerManager.pause() 暂停播放, tag=${currentState.currentlyPlayingTag}, isPlaying=${mediaPlayer?.isPlaying}`

**第 258 行** (string_literal):
```
"AudioPlayerManager.pause() 累加播放时长, tag=${currentState.currentlyPlayingTag}, segmentDuration=${segmentDuration}ms, totalAccumulated=${accumulatedTimeMs}ms",
```
中文内容: `AudioPlayerManager.pause() 累加播放时长, tag=${currentState.currentlyPlayingTag}, segmentDuration=${segmentDuration}ms, totalAccumulated=${accumulatedTimeMs}ms`

**第 273 行** (string_literal):
```
"AudioPlayerManager.pause() 暂停完成, tag=${currentState.currentlyPlayingTag}, position=${accumulatedTimeMs.toInt()}ms",
```
中文内容: `AudioPlayerManager.pause() 暂停完成, tag=${currentState.currentlyPlayingTag}, position=${accumulatedTimeMs.toInt()}ms`

**第 285 行** (string_literal):
```
"AudioPlayerManager.resume() 恢复播放, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}",
```
中文内容: `AudioPlayerManager.resume() 恢复播放, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}`

**第 293 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.resume() 重置播放起始时间, tag=${currentState.currentlyPlayingTag}, startTime=$playbackStartTimeMs")
```
中文内容: `AudioPlayerManager.resume() 重置播放起始时间, tag=${currentState.currentlyPlayingTag}, startTime=$playbackStartTimeMs`

**第 305 行** (string_literal):
```
"AudioPlayerManager.resume() 恢复播放完成, tag=${currentState.currentlyPlayingTag}, position=${currentState.currentPosition}ms",
```
中文内容: `AudioPlayerManager.resume() 恢复播放完成, tag=${currentState.currentlyPlayingTag}, position=${currentState.currentPosition}ms`

**第 317 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.release() 跳过释放，tag不匹配, 指定tag=$tag, 当前tag=$currentlyPlayingTag")
```
中文内容: `AudioPlayerManager.release() 跳过释放，tag不匹配, 指定tag=$tag, 当前tag=$currentlyPlayingTag`

**第 323 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.release() 跳过释放，tag不匹配, 当前tag=$currentlyPlayingTag")
```
中文内容: `AudioPlayerManager.release() 跳过释放，tag不匹配, 当前tag=$currentlyPlayingTag`

**第 341 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.release() 开始释放资源, tag=$currentTag, 当前tag=$currentTag")
```
中文内容: `AudioPlayerManager.release() 开始释放资源, tag=$currentTag, 当前tag=$currentTag`

**第 354 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.release() 释放完成, tag=$currentTag")
```
中文内容: `AudioPlayerManager.release() 释放完成, tag=$currentTag`

**第 365 行** (string_literal):
```
"AudioPlayerManager.seekTo() 跳转播放位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms, currentState=${currentState::class.simpleName}",
```
中文内容: `AudioPlayerManager.seekTo() 跳转播放位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms, currentState=${currentState::class.simpleName}`

**第 374 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.seekTo() 更新Playing状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms")
```
中文内容: `AudioPlayerManager.seekTo() 更新Playing状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms`

**第 379 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.seekTo() 更新Paused状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms")
```
中文内容: `AudioPlayerManager.seekTo() 更新Paused状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms`

**第 384 行** (string_literal):
```
"AudioPlayerManager.seekTo() 忽略seekTo，当前状态不支持, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}",
```
中文内容: `AudioPlayerManager.seekTo() 忽略seekTo，当前状态不支持, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}`

**第 396 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.startProgressUpdate() 开始进度更新, tag=${currentState.currentlyPlayingTag}")
```
中文内容: `AudioPlayerManager.startProgressUpdate() 开始进度更新, tag=${currentState.currentlyPlayingTag}`

**第 426 行** (string_literal):
```
LogUtils.d("AudioPlayerManager.stopProgressUpdate() 停止进度更新, tag=${currentState.currentlyPlayingTag}")
```
中文内容: `AudioPlayerManager.stopProgressUpdate() 停止进度更新, tag=${currentState.currentlyPlayingTag}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/manager/AudioRecordManager.kt
发现 21 个未本地化的中文字符串:

**第 81 行** (string_literal):
```
val channelDesc = if (config.audioChannels == 1) "单声道" else "立体声"
```
中文内容: `单声道`

**第 81 行** (string_literal):
```
val channelDesc = if (config.audioChannels == 1) "单声道" else "立体声"
```
中文内容: `立体声`

**第 84 行** (string_literal):
```
config.audioEncodingBitRate >= 128000 -> "高音质"
```
中文内容: `高音质`

**第 85 行** (string_literal):
```
config.audioEncodingBitRate >= 64000 -> "标准音质"
```
中文内容: `标准音质`

**第 86 行** (string_literal):
```
else -> "节省空间"
```
中文内容: `节省空间`

**第 259 行** (string_literal):
```
LogUtils.w("录音已在进行中，无法重复开始")
```
中文内容: `录音已在进行中，无法重复开始`

**第 275 行** (string_literal):
```
LogUtils.d("检测到暂停状态，自动恢复录音")
```
中文内容: `检测到暂停状态，自动恢复录音`

**第 319 行** (string_literal):
```
LogUtils.d("录音开始: ${outputFile.absolutePath}\n配置: $config")
```
中文内容: `录音开始: ${outputFile.absolutePath}\n配置: $config`

**第 352 行** (string_literal):
```
LogUtils.w("当前状态无法暂停录制")
```
中文内容: `当前状态无法暂停录制`

**第 370 行** (string_literal):
```
LogUtils.d("录音暂停")
```
中文内容: `录音暂停`

**第 390 行** (string_literal):
```
LogUtils.w("当前状态无法恢复录制")
```
中文内容: `当前状态无法恢复录制`

**第 419 行** (string_literal):
```
LogUtils.d("录音恢复")
```
中文内容: `录音恢复`

**第 440 行** (string_literal):
```
LogUtils.w("当前没有进行录制")
```
中文内容: `当前没有进行录制`

**第 483 行** (string_literal):
```
LogUtils.d("录音完成: ${currentOutputFile.absolutePath}, 总时长: ${duration}毫秒, 文件大小: ${fileSize}字节, 结束原因: $reasonText")
```
中文内容: `录音完成: ${currentOutputFile.absolutePath}, 总时长: ${duration}毫秒, 文件大小: ${fileSize}字节, 结束原因: $reasonText`

**第 512 行** (string_literal):
```
LogUtils.w("当前没有进行录制")
```
中文内容: `当前没有进行录制`

**第 529 行** (string_literal):
```
LogUtils.d("录音取消，临时文件已删除")
```
中文内容: `录音取消，临时文件已删除`

**第 566 行** (string_literal):
```
exception.message?.contains("存储空间不足") == true -> RecordingFailureReason.STORAGE_INSUFFICIENT
```
中文内容: `存储空间不足`

**第 612 行** (string_literal):
```
"录音错误: $message, 原因: $failureReason",
```
中文内容: `录音错误: $message, 原因: $failureReason`

**第 673 行** (string_literal):
```
LogUtils.w("检测到录音器异常，可能是系统中断: ${e.message}")
```
中文内容: `检测到录音器异常，可能是系统中断: ${e.message}`

**第 692 行** (string_literal):
```
LogUtils.d("达到最大录制时长，自动停止录制")
```
中文内容: `达到最大录制时长，自动停止录制`

**第 729 行** (string_literal):
```
"释放录制器失败",
```
中文内容: `释放录制器失败`

----------------------------------------

## app/src/main/java/com/buque/wakoo/manager/EnvironmentManager.kt
发现 1 个未本地化的中文字符串:

**第 65 行** (string_literal):
```
showToast("没有找到合适的环境：$currentEnvironmentKey，已自动调整为：${key}环境")
```
中文内容: `没有找到合适的环境：$currentEnvironmentKey，已自动调整为：${key}环境`

----------------------------------------

## app/src/main/java/com/buque/wakoo/manager/L10nManager.kt
发现 2 个未本地化的中文字符串:

**第 131 行** (string_literal):
```
LogUtils.dTag("L10nManager", "加载国际化包成功: $tag")
```
中文内容: `加载国际化包成功: $tag`

**第 134 行** (string_literal):
```
LogUtils.eTag("L10nManager", "加载国际化包失败: $tag")
```
中文内容: `加载国际化包失败: $tag`

----------------------------------------

## app/src/main/java/com/buque/wakoo/navigation/AppNavController.kt
发现 2 个未本地化的中文字符串:

**第 319 行** (string_literal):
```
error("不支持，请使用pushTopLevel")
```
中文内容: `不支持，请使用pushTopLevel`

**第 325 行** (string_literal):
```
error("不支持，请使用pushTopLevel")
```
中文内容: `不支持，请使用pushTopLevel`

----------------------------------------

## app/src/main/java/com/buque/wakoo/network/ApiClient.kt
发现 1 个未本地化的中文字符串:

**第 122 行** (string_literal):
```
LogUtils.d("ApiClient 正在尝试使用 RefreshToken: $refreshTokenValue 进行刷新...")
```
中文内容: `ApiClient 正在尝试使用 RefreshToken: $refreshTokenValue 进行刷新...`

----------------------------------------

## app/src/main/java/com/buque/wakoo/network/ApiHandler.kt
发现 1 个未本地化的中文字符串:

**第 118 行** (string_literal):
```
LogUtils.eTag("ApiClient", "数据解析异常：$e", showCallerInfo = false, showThreadInfo = false)
```
中文内容: `数据解析异常：$e`

----------------------------------------

## app/src/main/java/com/buque/wakoo/network/AuthenticationInterceptor.kt
发现 10 个未本地化的中文字符串:

**第 108 行** (string_literal):
```
LogUtils.d("TokenRefreshInterceptor: 检测到Token过期, 准备刷新. Request: ${originalRequest.url}")
```
中文内容: `TokenRefreshInterceptor: 检测到Token过期, 准备刷新. Request: ${originalRequest.url}`

**第 143 行** (string_literal):
```
LogUtils.d("TokenRefreshInterceptor: Token已被刷新, 直接使用新Token重试. Request: ${originalRequest.url}")
```
中文内容: `TokenRefreshInterceptor: Token已被刷新, 直接使用新Token重试. Request: ${originalRequest.url}`

**第 150 行** (string_literal):
```
LogUtils.d("TokenRefreshInterceptor: 开始执行Token刷新操作.")
```
中文内容: `TokenRefreshInterceptor: 开始执行Token刷新操作.`

**第 167 行** (string_literal):
```
LogUtils.e("TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.")
```
中文内容: `TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.`

**第 179 行** (string_literal):
```
LogUtils.w("TokenRefreshInterceptor: RefreshToken已过期, 执行登出.")
```
中文内容: `TokenRefreshInterceptor: RefreshToken已过期, 执行登出.`

**第 187 行** (string_literal):
```
LogUtils.e("TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.")
```
中文内容: `TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.`

**第 193 行** (string_literal):
```
LogUtils.w("TokenRefreshInterceptor: 首次Token刷新失败, 准备重试一次. Error: ${refreshResult.exceptionOrNull()?.message}")
```
中文内容: `TokenRefreshInterceptor: 首次Token刷新失败, 准备重试一次. Error: ${refreshResult.exceptionOrNull()?.message}`

**第 203 行** (string_literal):
```
LogUtils.d("TokenRefreshInterceptor: Token刷新成功!")
```
中文内容: `TokenRefreshInterceptor: Token刷新成功!`

**第 207 行** (string_literal):
```
LogUtils.e("TokenRefreshInterceptor: Token刷新彻底失败. Error: ${refreshResult.exceptionOrNull()?.message}")
```
中文内容: `TokenRefreshInterceptor: Token刷新彻底失败. Error: ${refreshResult.exceptionOrNull()?.message}`

**第 227 行** (string_literal):
```
LogUtils.d("TokenRefreshInterceptor: 使用新Token继续请求. New Token: $accessToken, Request: ${request.url}")
```
中文内容: `TokenRefreshInterceptor: 使用新Token继续请求. New Token: $accessToken, Request: ${request.url}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/network/ErrorHandlingInterceptor.kt
发现 3 个未本地化的中文字符串:

**第 141 行** (string_literal):
```
LogUtils.e("ErrorHandlingInterceptor: 网络连接异常 - ${e.javaClass.simpleName}: ${e.message}")
```
中文内容: `ErrorHandlingInterceptor: 网络连接异常 - ${e.javaClass.simpleName}: ${e.message}`

**第 148 行** (string_literal):
```
LogUtils.e("ErrorHandlingInterceptor: 通用IO异常 - ${e.javaClass.simpleName}: ${e.message} $request")
```
中文内容: `ErrorHandlingInterceptor: 通用IO异常 - ${e.javaClass.simpleName}: ${e.message} $request`

**第 161 行** (string_literal):
```
LogUtils.e("ErrorHandlingInterceptor: 未知错误 - ${e.javaClass.simpleName}: ${e.message}")
```
中文内容: `ErrorHandlingInterceptor: 未知错误 - ${e.javaClass.simpleName}: ${e.message}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/network/api/bean/LoginApiBean.kt
发现 5 个未本地化的中文字符串:

**第 30 行** (string_literal):
```
* (301, "WAKOO_PHONE", "Wakoo手机号登录")
```
中文内容: `Wakoo手机号登录`

**第 31 行** (string_literal):
```
* (302, "WAKOO_GOOGLE", "Wakoo Google登录")
```
中文内容: `Wakoo Google登录`

**第 32 行** (string_literal):
```
* (307, "WAKOO_FACEBOOK2", "Wakoo FB登录")
```
中文内容: `Wakoo FB登录`

**第 33 行** (string_literal):
```
* (308, "WAKOO_LINE", "Wakoo Line登录")
```
中文内容: `Wakoo Line登录`

**第 34 行** (string_literal):
```
* (305, "WAKOO_FAST_LOGIN_ANDROID", "Wakoo Android快速登录")
```
中文内容: `Wakoo Android快速登录`

----------------------------------------

## app/src/main/java/com/buque/wakoo/network/api/bean/UserApiBean.kt
发现 2 个未本地化的中文字符串:

**第 34 行** (string_literal):
```
"region_label": "中国大陆",
```
中文内容: `中国大陆`

**第 36 行** (string_literal):
```
"region_reason_label": "现居地",
```
中文内容: `现居地`

----------------------------------------

## app/src/main/java/com/buque/wakoo/network/api/bean/VoiceFeedResponse.kt
发现 3 个未本地化的中文字符串:

**第 54 行** (string_literal):
```
* "id": 1, "name": "测试多语言问题" }, { "id": 2, "name": "美好的日子" }, { "id": 5, "name": "青春" } ],
```
中文内容: `测试多语言问题`

**第 54 行** (string_literal):
```
* "id": 1, "name": "测试多语言问题" }, { "id": 2, "name": "美好的日子" }, { "id": 5, "name": "青春" } ],
```
中文内容: `美好的日子`

**第 54 行** (string_literal):
```
* "id": 1, "name": "测试多语言问题" }, { "id": 2, "name": "美好的日子" }, { "id": 5, "name": "青春" } ],
```
中文内容: `青春`

----------------------------------------

## app/src/main/java/com/buque/wakoo/network/api/bean/WalletBean.kt
发现 5 个未本地化的中文字符串:

**第 32 行** (string_literal):
```
*     (2, "APPLE_SDK", "苹果SDK"),
```
中文内容: `苹果SDK`

**第 33 行** (string_literal):
```
*     (3, "WXPAY_SDK", "微信SDK"),
```
中文内容: `微信SDK`

**第 34 行** (string_literal):
```
*     (4, "ORDER_LINK", "获取订单链接后打开"),
```
中文内容: `获取订单链接后打开`

**第 36 行** (string_literal):
```
*     (6, "LINK_WEB", "浏览器打开链接"),
```
中文内容: `浏览器打开链接`

**第 37 行** (string_literal):
```
*     (7, "AGENT", "代充"),
```
中文内容: `代充`

----------------------------------------

## app/src/main/java/com/buque/wakoo/network/api/service/GiftApiService.kt
发现 10 个未本地化的中文字符串:

**第 25 行** (string_literal):
```
*      *    (0, "UNKNOWN", "未知"),
```
中文内容: `未知`

**第 26 行** (string_literal):
```
*      *     (1, "TRIBE", "部落"),
```
中文内容: `部落`

**第 27 行** (string_literal):
```
*      *     (2, "AUDIOROOM", "语音房"),
```
中文内容: `语音房`

**第 28 行** (string_literal):
```
*      *     (3, "RPIVATEROOM", "私密小屋"),
```
中文内容: `私密小屋`

**第 29 行** (string_literal):
```
*      *     (4, "RPIVATE_CHAT", "私聊"),
```
中文内容: `私聊`

**第 30 行** (string_literal):
```
*      *     (5, "MOMENT", "动态"),
```
中文内容: `动态`

**第 31 行** (string_literal):
```
*      *     (6, "PROFILE", "个人主页"),
```
中文内容: `个人主页`

**第 32 行** (string_literal):
```
*      *     (7, "CHATGROUP", "群聊"),
```
中文内容: `群聊`

**第 33 行** (string_literal):
```
*      *     (8, "AUDIO_CHAT_USER_RECOM", "语音聊天用户推荐"),
```
中文内容: `语音聊天用户推荐`

**第 34 行** (string_literal):
```
*      *     (9, "CP_ROOM", "CP小屋"),
```
中文内容: `CP小屋`

----------------------------------------

## app/src/main/java/com/buque/wakoo/repository/AccountPreferencesRepository.kt
发现 6 个未本地化的中文字符串:

**第 62 行** (string_literal):
```
LogUtils.d("没有找到登录信息")
```
中文内容: `没有找到登录信息`

**第 71 行** (string_literal):
```
LogUtils.d("从JSON读取到用户信息: id=${it.id}, name=${it.name}")
```
中文内容: `从JSON读取到用户信息: id=${it.id}, name=${it.name}`

**第 77 行** (string_literal):
```
LogUtils.e(e, "读取用户信息失败")
```
中文内容: `读取用户信息失败`

**第 106 行** (string_literal):
```
LogUtils.d("保存用户信息成功: id=${accountInfo.id}, name=${accountInfo.name}")
```
中文内容: `保存用户信息成功: id=${accountInfo.id}, name=${accountInfo.name}`

**第 111 行** (string_literal):
```
LogUtils.e(e, "保存用户信息失败: ${e.message}")
```
中文内容: `保存用户信息失败: ${e.message}`

**第 122 行** (string_literal):
```
LogUtils.d("清除用户信息成功")
```
中文内容: `清除用户信息成功`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/dialog/CommonDialogs.kt
发现 17 个未本地化的中文字符串:

**第 495 行** (string_literal):
```
title = "注销账号",
```
中文内容: `注销账号`

**第 496 行** (string_literal):
```
content = "注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。",
```
中文内容: `注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。`

**第 497 行** (string_literal):
```
cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消"),
```
中文内容: `取消`

**第 498 行** (string_literal):
```
confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "确认注销"),
```
中文内容: `确认注销`

**第 521 行** (string_literal):
```
title = "注销账号",
```
中文内容: `注销账号`

**第 522 行** (string_literal):
```
content = "注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。",
```
中文内容: `注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。`

**第 523 行** (string_literal):
```
confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "取消"),
```
中文内容: `取消`

**第 524 行** (string_literal):
```
cancelButtonConfig = DialogButtonStyles.Transparent.copy(text = "确认注销"),
```
中文内容: `确认注销`

**第 547 行** (string_literal):
```
content = "本次选择的卡片主题需要花费200钻石，您的钻石余额不足，请充值",
```
中文内容: `本次选择的卡片主题需要花费200钻石，您的钻石余额不足，请充值`

**第 548 行** (string_literal):
```
buttonConfig = DialogButtonStyles.Primary.copy(text = "充值钻石"),
```
中文内容: `充值钻石`

**第 554 行** (string_literal):
```
content = "本次选择的卡片主题仅会员用户可以使用，快去开通会员吧！",
```
中文内容: `本次选择的卡片主题仅会员用户可以使用，快去开通会员吧！`

**第 555 行** (string_literal):
```
buttonConfig = DialogButtonStyles.VIP.copy(text = "开通会员"),
```
中文内容: `开通会员`

**第 651 行** (string_literal):
```
content = "本次选择的卡片主题需要花费200钻石，确认扣除钻石并发布作品吗？",
```
中文内容: `本次选择的卡片主题需要花费200钻石，确认扣除钻石并发布作品吗？`

**第 652 行** (string_literal):
```
cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消"),
```
中文内容: `取消`

**第 653 行** (string_literal):
```
confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "确认"),
```
中文内容: `确认`

**第 673 行** (string_literal):
```
title = "内容合规准则",
```
中文内容: `内容合规准则`

**第 674 行** (string_literal):
```
content = "我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。\n\n我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。",
```
中文内容: `我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。\n\n我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/dialog/FilterUserAreaWidget.kt
发现 1 个未本地化的中文字符串:

**第 128 行** (string_literal):
```
text = "开始筛选",
```
中文内容: `开始筛选`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/dialog/FirstClassNotifyDialog.kt
发现 3 个未本地化的中文字符串:

**第 289 行** (string_literal):
```
headMessage = "恭喜您",
```
中文内容: `恭喜您`

**第 290 行** (string_literal):
```
middleMessage = "成為尊敬的頭等艙俱樂部成員",
```
中文内容: `成為尊敬的頭等艙俱樂部成員`

**第 292 行** (string_literal):
```
message = "所有俱樂部成員特權已解鎖,祝您使用愉快",
```
中文内容: `所有俱樂部成員特權已解鎖,祝您使用愉快`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/dialog/PrivateRoomNotificationDialog.kt
发现 4 个未本地化的中文字符串:

**第 209 行** (string_literal):
```
content = "您的免费时长已不足，\n可添加好友享有无限通话时长。",
```
中文内容: `您的免费时长已不足，\n可添加好友享有无限通话时长。`

**第 210 行** (string_literal):
```
button = "加好友",
```
中文内容: `加好友`

**第 292 行** (string_literal):
```
append("丨")
```
中文内容: `丨`

**第 407 行** (string_literal):
```
hint = "她正在找人语音连麦聊天...",
```
中文内容: `她正在找人语音连麦聊天...`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/dialog/TaskRewardDialog.kt
发现 2 个未本地化的中文字符串:

**第 280 行** (string_literal):
```
title = "这个是标题",
```
中文内容: `这个是标题`

**第 288 行** (string_literal):
```
desc = "这是测试的内容这是测试的内容这是测试的内容这是测试的内容这是测试的内容",
```
中文内容: `这是测试的内容这是测试的内容这是测试的内容这是测试的内容这是测试的内容`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/dialog/UserTaskFinishedDialog.kt
发现 3 个未本地化的中文字符串:

**第 76 行** (string_literal):
```
"恭喜聊天任务完成", "恭喜获得了xxxx积分", "领取现金"
```
中文内容: `恭喜聊天任务完成`

**第 76 行** (string_literal):
```
"恭喜聊天任务完成", "恭喜获得了xxxx积分", "领取现金"
```
中文内容: `恭喜获得了xxxx积分`

**第 76 行** (string_literal):
```
"恭喜聊天任务完成", "恭喜获得了xxxx积分", "领取现金"
```
中文内容: `领取现金`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/floating/BuddyFloating.kt
发现 1 个未本地化的中文字符串:

**第 292 行** (string_literal):
```
"哈哈哈",
```
中文内容: `哈哈哈`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/ChatGroupEntryCard.kt
发现 1 个未本地化的中文字符串:

**第 80 行** (string_literal):
```
ChatGroupEntryCard(ChatGroupBean(name = "星空闪耀", memberCnt = 122))
```
中文内容: `星空闪耀`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/chat/ChatGroupMessageUI.kt
发现 4 个未本地化的中文字符串:

**第 270 行** (string_literal):
```
append("恭喜新人")
```
中文内容: `恭喜新人`

**第 274 行** (string_literal):
```
append("加入群组")
```
中文内容: `加入群组`

**第 379 行** (string_literal):
```
append("送给")
```
中文内容: `送给`

**第 385 行** (string_literal):
```
messageGift = buildAnnotatedString { append("梦幻城包x1") },
```
中文内容: `梦幻城包x1`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/member/GroupMemberList.kt
发现 3 个未本地化的中文字符串:

**第 86 行** (string_literal):
```
"周活跃度", modifier = Modifier
```
中文内容: `周活跃度`

**第 104 行** (string_literal):
```
"总活跃度", modifier = Modifier
```
中文内容: `总活跃度`

**第 159 行** (string_literal):
```
TextSort("周活跃度")
```
中文内容: `周活跃度`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/panel/EditGroupInfoPanel.kt
发现 4 个未本地化的中文字符串:

**第 95 行** (string_literal):
```
title = "修改群组名称",
```
中文内容: `修改群组名称`

**第 97 行** (string_literal):
```
placeholder = "请输入家族昵称",
```
中文内容: `请输入家族昵称`

**第 112 行** (string_literal):
```
title = "修改群组简介",
```
中文内容: `修改群组简介`

**第 114 行** (string_literal):
```
placeholder = "请输入家族昵称",
```
中文内容: `请输入家族昵称`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/screen/ChatGroupDescriptionScreen.kt
发现 4 个未本地化的中文字符串:

**第 604 行** (string_literal):
```
name = "星空闪耀",
```
中文内容: `星空闪耀`

**第 608 行** (string_literal):
```
"        \"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\" +\n" +
```
中文内容: `在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\`

**第 609 行** (string_literal):
```
"            \"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\" +\n" +
```
中文内容: `在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\`

**第 610 行** (string_literal):
```
"            \"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\"",
```
中文内容: `在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/screen/ChatGroupSettings.kt
发现 6 个未本地化的中文字符串:

**第 508 行** (string_literal):
```
name = "群聊名称哈哈哈",
```
中文内容: `群聊名称哈哈哈`

**第 509 行** (string_literal):
```
bulletin = "介绍介绍介绍介绍介绍介绍介...",
```
中文内容: `介绍介绍介绍介绍介绍介绍介...`

**第 523 行** (string_literal):
```
name = "群聊名称哈哈哈",
```
中文内容: `群聊名称哈哈哈`

**第 525 行** (string_literal):
```
bulletin = "介绍介绍介绍介绍介绍介绍介...",
```
中文内容: `介绍介绍介绍介绍介绍介绍介...`

**第 535 行** (string_literal):
```
val group = ChatGroupBean("123456", name = "群聊名称哈哈哈", bulletin = "介绍介绍介绍介绍介绍介绍介...")
```
中文内容: `群聊名称哈哈哈`

**第 535 行** (string_literal):
```
val group = ChatGroupBean("123456", name = "群聊名称哈哈哈", bulletin = "介绍介绍介绍介绍介绍介绍介...")
```
中文内容: `介绍介绍介绍介绍介绍介绍介...`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/screen/ChatGroupSquareScreen.kt
发现 1 个未本地化的中文字符串:

**第 360 行** (string_literal):
```
text = "${item.memberCnt}人",
```
中文内容: `${item.memberCnt}人`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/tasks/models/ActiveTaskInfo.kt
发现 7 个未本地化的中文字符串:

**第 12 行** (string_literal):
```
*     CHECK_IN = 1, "签到"
```
中文内容: `签到`

**第 13 行** (string_literal):
```
*     GIVE_GIFT = 2, "送礼"
```
中文内容: `送礼`

**第 14 行** (string_literal):
```
*     SEND_MESSAGE = 3, "发言"
```
中文内容: `发言`

**第 15 行** (string_literal):
```
*     FIRST_GIVE_GIFT = 4, "首次送礼"
```
中文内容: `首次送礼`

**第 16 行** (string_literal):
```
*     SEND_MESSAGE_WITH_CP = 5, "与 CP一起发言"
```
中文内容: `与 CP一起发言`

**第 19 行** (string_literal):
```
*     FIRST_MAKE_CP = 1, "首次组CP"
```
中文内容: `首次组CP`

**第 20 行** (string_literal):
```
*     FIRST_PUBLIC_CP = 2, "首次官宣CP"
```
中文内容: `首次官宣CP`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/tasks/models/TribeActiveInfo.kt
发现 1 个未本地化的中文字符串:

**第 17 行** (string_literal):
```
*     "bonus_desc": "奖励描述"
```
中文内容: `奖励描述`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/chatgroup/tasks/ui/GroupTaskUI.kt
发现 22 个未本地化的中文字符串:

**第 739 行** (string_literal):
```
add(BonusLevel(1, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `勋章*8天`

**第 739 行** (string_literal):
```
add(BonusLevel(1, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `名称`

**第 739 行** (string_literal):
```
add(BonusLevel(1, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `勋章*8天`

**第 739 行** (string_literal):
```
add(BonusLevel(1, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `名称`

**第 740 行** (string_literal):
```
add(BonusLevel(2, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `勋章*8天`

**第 740 行** (string_literal):
```
add(BonusLevel(2, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `名称`

**第 740 行** (string_literal):
```
add(BonusLevel(2, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `勋章*8天`

**第 740 行** (string_literal):
```
add(BonusLevel(2, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `名称`

**第 741 行** (string_literal):
```
add(BonusLevel(4, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `勋章*8天`

**第 741 行** (string_literal):
```
add(BonusLevel(4, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `名称`

**第 741 行** (string_literal):
```
add(BonusLevel(4, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `勋章*8天`

**第 741 行** (string_literal):
```
add(BonusLevel(4, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
```
中文内容: `名称`

**第 744 行** (string_literal):
```
ActiveReward(title = "恭喜获得", icon = "", desc = "活跃度+10", buttonText = "开心收下")
```
中文内容: `活跃度+10`

**第 839 行** (string_literal):
```
taskName = "群组发言",
```
中文内容: `群组发言`

**第 843 行** (string_literal):
```
add(ActiveTask(taskName = "群组签到", taskBtnLabel = "签到"))
```
中文内容: `群组签到`

**第 843 行** (string_literal):
```
add(ActiveTask(taskName = "群组签到", taskBtnLabel = "签到"))
```
中文内容: `签到`

**第 844 行** (string_literal):
```
add(ActiveTask(taskName = "赠送任意U币礼物", taskBtnLabel = "去完成"))
```
中文内容: `赠送任意U币礼物`

**第 844 行** (string_literal):
```
add(ActiveTask(taskName = "赠送任意U币礼物", taskBtnLabel = "去完成"))
```
中文内容: `去完成`

**第 847 行** (string_literal):
```
taskName = "在群组中送1次礼物",
```
中文内容: `在群组中送1次礼物`

**第 848 行** (string_literal):
```
taskBtnLabel = "已完成",
```
中文内容: `已完成`

**第 854 行** (string_literal):
```
taskName = "与CP在群组中共同发言",
```
中文内容: `与CP在群组中共同发言`

**第 855 行** (string_literal):
```
taskBtnLabel = "已完成",
```
中文内容: `已完成`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/crony/CronySettingsScreen.kt
发现 1 个未本地化的中文字符串:

**第 91 行** (string_literal):
```
"解除关系",
```
中文内容: `解除关系`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/crony/CronyUI.kt
发现 7 个未本地化的中文字符串:

**第 475 行** (string_literal):
```
CronyTag(name = "你大爷"),
```
中文内容: `你大爷`

**第 476 行** (string_literal):
```
CronyTag(name = "你二爷"),
```
中文内容: `你二爷`

**第 477 行** (string_literal):
```
CronyTag(name = "你三爷"),
```
中文内容: `你三爷`

**第 478 行** (string_literal):
```
CronyTag(name = "你四爷"),
```
中文内容: `你四爷`

**第 479 行** (string_literal):
```
CronyTag(name = "你五爷"),
```
中文内容: `你五爷`

**第 480 行** (string_literal):
```
CronyTag(name = "你六爷"),
```
中文内容: `你六爷`

**第 481 行** (string_literal):
```
CronyTag(name = "你七爷"),
```
中文内容: `你七爷`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugAccountManger.kt
发现 25 个未本地化的中文字符串:

**第 119 行** (string_literal):
```
text = "账号管理",
```
中文内容: `账号管理`

**第 129 行** (string_literal):
```
contentDescription = "关闭",
```
中文内容: `关闭`

**第 136 行** (string_literal):
```
Text(text = "新增账号", style = MaterialTheme.typography.labelLarge)
```
中文内容: `新增账号`

**第 157 行** (string_literal):
```
placeholder = "手机号",
```
中文内容: `手机号`

**第 171 行** (string_literal):
```
placeholder = "备注",
```
中文内容: `备注`

**第 181 行** (string_literal):
```
showToast("手机号必须是11位")
```
中文内容: `手机号必须是11位`

**第 185 行** (string_literal):
```
showToast("账号已存在")
```
中文内容: `账号已存在`

**第 192 行** (string_literal):
```
showToast("已保存")
```
中文内容: `已保存`

**第 196 行** (string_literal):
```
contentDescription = "新增",
```
中文内容: `新增`

**第 203 行** (string_literal):
```
Text(text = "自动注册号前缀和后缀（自动随机中间数字补齐11位）", style = MaterialTheme.typography.labelLarge)
```
中文内容: `自动注册号前缀和后缀（自动随机中间数字补齐11位）`

**第 239 行** (string_literal):
```
placeholder = "注册号段前缀，默认是000开头",
```
中文内容: `注册号段前缀，默认是000开头`

**第 253 行** (string_literal):
```
placeholder = "注册号段后缀，默认是空结尾",
```
中文内容: `注册号段后缀，默认是空结尾`

**第 268 行** (string_literal):
```
text = if (selectedOptionMale) "男号配置" else "女号配置",
```
中文内容: `男号配置`

**第 268 行** (string_literal):
```
text = if (selectedOptionMale) "男号配置" else "女号配置",
```
中文内容: `女号配置`

**第 279 行** (string_literal):
```
text = { Text(if (it == 0) "男号配置" else "女号配置") },
```
中文内容: `男号配置`

**第 279 行** (string_literal):
```
text = { Text(if (it == 0) "男号配置" else "女号配置") },
```
中文内容: `女号配置`

**第 291 行** (string_literal):
```
showToast("前缀或后缀长度不能大于4")
```
中文内容: `前缀或后缀长度不能大于4`

**第 296 行** (string_literal):
```
showToast("已保存")
```
中文内容: `已保存`

**第 300 行** (string_literal):
```
contentDescription = "保存",
```
中文内容: `保存`

**第 308 行** (string_literal):
```
Text(text = "账号列表", style = MaterialTheme.typography.labelLarge)
```
中文内容: `账号列表`

**第 331 行** (string_literal):
```
showToast("已复制")
```
中文内容: `已复制`

**第 337 行** (string_literal):
```
"${it.account}(当前账号)"
```
中文内容: `${it.account}(当前账号)`

**第 352 行** (string_literal):
```
Text(text = "删除", style = MaterialTheme.typography.labelLarge)
```
中文内容: `删除`

**第 365 行** (string_literal):
```
Text(text = "切换", style = MaterialTheme.typography.labelLarge)
```
中文内容: `切换`

**第 381 行** (string_literal):
```
Text("关闭")
```
中文内容: `关闭`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugEnvironmentSwitchDialog.kt
发现 8 个未本地化的中文字符串:

**第 93 行** (string_literal):
```
text = "环境切换",
```
中文内容: `环境切换`

**第 101 行** (string_literal):
```
contentDescription = "关闭",
```
中文内容: `关闭`

**第 120 行** (string_literal):
```
text = "当前环境",
```
中文内容: `当前环境`

**第 138 行** (string_literal):
```
text = "可用环境",
```
中文内容: `可用环境`

**第 164 行** (string_literal):
```
text = "没有可用的环境配置",
```
中文内容: `没有可用的环境配置`

**第 191 行** (string_literal):
```
Text("重置")
```
中文内容: `重置`

**第 205 行** (string_literal):
```
Text("保存")
```
中文内容: `保存`

**第 279 行** (string_literal):
```
contentDescription = "已选中",
```
中文内容: `已选中`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugFloatingButton.kt
发现 1 个未本地化的中文字符串:

**第 86 行** (string_literal):
```
"调试工具",
```
中文内容: `调试工具`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugGitCommitsDialog.kt
发现 5 个未本地化的中文字符串:

**第 101 行** (string_literal):
```
text = "Git 提交记录",
```
中文内容: `Git 提交记录`

**第 109 行** (string_literal):
```
contentDescription = "关闭",
```
中文内容: `关闭`

**第 126 行** (string_literal):
```
text = "无法获取 Git 提交记录",
```
中文内容: `无法获取 Git 提交记录`

**第 145 行** (string_literal):
```
showToast("已复制")
```
中文内容: `已复制`

**第 160 行** (string_literal):
```
Text("关闭")
```
中文内容: `关闭`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugMenu.kt
发现 38 个未本地化的中文字符串:

**第 111 行** (string_literal):
```
text = "调试菜单",
```
中文内容: `调试菜单`

**第 119 行** (string_literal):
```
contentDescription = "关闭",
```
中文内容: `关闭`

**第 144 行** (string_literal):
```
text = "账号信息",
```
中文内容: `账号信息`

**第 161 行** (string_literal):
```
InfoRow("手机号", DevicesKV.decodeString("_debug_current_phone").orEmpty())
```
中文内容: `手机号`

**第 162 行** (string_literal):
```
InfoRow("设备id", DeviceInfoManager.deviceId)
```
中文内容: `设备id`

**第 164 行** (string_literal):
```
InfoRow("是否是主播", SelfUser?.isHQU?.toString().orEmpty())
```
中文内容: `是否是主播`

**第 165 行** (string_literal):
```
InfoRow("是否是日区", SelfUser?.isJP?.toString().orEmpty())
```
中文内容: `是否是日区`

**第 190 行** (string_literal):
```
text = "应用信息",
```
中文内容: `应用信息`

**第 207 行** (string_literal):
```
InfoRow("版本", "${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})")
```
中文内容: `版本`

**第 208 行** (string_literal):
```
InfoRow("构建类型", BuildConfig.BUILD_TYPE)
```
中文内容: `构建类型`

**第 209 行** (string_literal):
```
InfoRow("构建环境", BuildConfig.FLAVOR_environment)
```
中文内容: `构建环境`

**第 210 行** (string_literal):
```
InfoRow("渠道", BuildConfig.FLAVOR_channel)
```
中文内容: `渠道`

**第 211 行** (string_literal):
```
InfoRow("当前环境", EnvironmentManager.getCurrentEnvironmentKey())
```
中文内容: `当前环境`

**第 236 行** (string_literal):
```
text = "Git 信息",
```
中文内容: `Git 信息`

**第 255 行** (string_literal):
```
InfoRow("分支", BuildConfig.GIT_BRANCH)
```
中文内容: `分支`

**第 256 行** (string_literal):
```
InfoRow("提交", BuildConfig.GIT_SHORT_COMMIT_HASH)
```
中文内容: `提交`

**第 257 行** (string_literal):
```
InfoRow("消息", BuildConfig.GIT_COMMIT_MESSAGE)
```
中文内容: `消息`

**第 258 行** (string_literal):
```
InfoRow("作者", BuildConfig.GIT_COMMIT_AUTHOR)
```
中文内容: `作者`

**第 259 行** (string_literal):
```
InfoRow("提交时间", BuildConfig.GIT_COMMIT_DATE)
```
中文内容: `提交时间`

**第 261 行** (string_literal):
```
"构建时间",
```
中文内容: `构建时间`

**第 273 行** (string_literal):
```
text = "调试功能",
```
中文内容: `调试功能`

**第 291 行** (string_literal):
```
title = "环境切换",
```
中文内容: `环境切换`

**第 292 行** (string_literal):
```
description = "切换到不同的环境配置",
```
中文内容: `切换到不同的环境配置`

**第 301 行** (string_literal):
```
title = "Git 提交记录",
```
中文内容: `Git 提交记录`

**第 302 行** (string_literal):
```
description = "查看最近5条提交记录",
```
中文内容: `查看最近5条提交记录`

**第 311 行** (string_literal):
```
title = "日志开关: ${if (EnvironmentManager.current.enableLog) "on" else "off"}",
```
中文内容: `日志开关: ${if (EnvironmentManager.current.enableLog) `

**第 312 行** (string_literal):
```
description = "切换将重启app",
```
中文内容: `切换将重启app`

**第 322 行** (string_literal):
```
description = "调试页面",
```
中文内容: `调试页面`

**第 332 行** (string_literal):
```
title = "退出登录",
```
中文内容: `退出登录`

**第 333 行** (string_literal):
```
description = "返回到登录页面",
```
中文内容: `返回到登录页面`

**第 345 行** (string_literal):
```
title = "账号管理",
```
中文内容: `账号管理`

**第 346 行** (string_literal):
```
description = "管理保存常用号码方便切换",
```
中文内容: `管理保存常用号码方便切换`

**第 356 行** (string_literal):
```
title = "一键注册女号",
```
中文内容: `一键注册女号`

**第 357 行** (string_literal):
```
description = "手机号随机(可以设置号段)",
```
中文内容: `手机号随机(可以设置号段)`

**第 378 行** (string_literal):
```
title = "一键注册男号",
```
中文内容: `一键注册男号`

**第 379 行** (string_literal):
```
description = "手机号随机(可以设置号段)",
```
中文内容: `手机号随机(可以设置号段)`

**第 406 行** (string_literal):
```
Text("关闭")
```
中文内容: `关闭`

**第 460 行** (string_literal):
```
showToast("已复制")
```
中文内容: `已复制`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/debug/DebugScreen.kt
发现 12 个未本地化的中文字符串:

**第 83 行** (string_literal):
```
Text("H5 测试")
```
中文内容: `H5 测试`

**第 88 行** (string_literal):
```
Text("本地H5 测试")
```
中文内容: `本地H5 测试`

**第 102 行** (string_literal):
```
Text("PUQC测试")
```
中文内容: `PUQC测试`

**第 113 行** (string_literal):
```
Text("装扮商城")
```
中文内容: `装扮商城`

**第 116 行** (string_literal):
```
SolidButton("群组", onClick = {
```
中文内容: `群组`

**第 127 行** (string_literal):
```
SolidButton("打开群组", onClick = {
```
中文内容: `打开群组`

**第 131 行** (string_literal):
```
SolidButton("打开个人主页", onClick = {
```
中文内容: `打开个人主页`

**第 138 行** (string_literal):
```
Text("打开用户主页")
```
中文内容: `打开用户主页`

**第 147 行** (string_literal):
```
Text("打开提现")
```
中文内容: `打开提现`

**第 152 行** (string_literal):
```
Text("打开Report")
```
中文内容: `打开Report`

**第 158 行** (string_literal):
```
Text("打开通知")
```
中文内容: `打开通知`

**第 238 行** (string_literal):
```
Text("打开图库")
```
中文内容: `打开图库`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/dressup/SilverShopListScreen.kt
发现 2 个未本地化的中文字符串:

**第 691 行** (string_literal):
```
text = "总计需要消耗银币：%s\n兑换成功后将发放至礼物背包".localizedFormatWithKey("总计需要消耗银币", totalPrice),
```
中文内容: `总计需要消耗银币`

**第 741 行** (string_literal):
```
ExchangeItemsDialogContent("", "基督教", 100) {}
```
中文内容: `基督教`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/home/<USER>
发现 1 个未本地化的中文字符串:

**第 539 行** (string_literal):
```
contentDescription = "钻石",
```
中文内容: `钻石`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/japan/boost/BoostWidgets.kt
发现 14 个未本地化的中文字符串:

**第 518 行** (string_literal):
```
PWidget("379191", "说明：钻石可用于App内购买卡片背景，装扮，礼物等道具", "兑换现金", recordVisible = true, qaVisible = true)
```
中文内容: `说明：钻石可用于App内购买卡片背景，装扮，礼物等道具`

**第 520 行** (string_literal):
```
CWidget("我的现金", "23929", "出金")
```
中文内容: `我的现金`

**第 520 行** (string_literal):
```
CWidget("我的现金", "23929", "出金")
```
中文内容: `出金`

**第 523 行** (string_literal):
```
"友達を招待して登録し、 ダイヤをゲット",
```
中文内容: `友達を招待して登録し、 ダイヤをゲット`

**第 524 行** (string_literal):
```
"友達を1人招待するごとに、5000ダイヤが報酬としてもらえます。 友達がUCOOでコインをチャージしたり、ダイヤをゲットしたりすると、あなたはダイヤがもらえます。",
```
中文内容: `友達を1人招待するごとに、5000ダイヤが報酬としてもらえます。 友達がUCOOでコインをチャージしたり、ダイヤをゲットしたりすると、あなたはダイヤがもらえます。`

**第 525 行** (string_literal):
```
"今すぐ招待",
```
中文内容: `今すぐ招待`

**第 621 行** (string_literal):
```
MissionCompleteContent("50积分", "完成“填写所有个人基础信息”任务，获得积分奖励") { }
```
中文内容: `50积分`

**第 621 行** (string_literal):
```
MissionCompleteContent("50积分", "完成“填写所有个人基础信息”任务，获得积分奖励") { }
```
中文内容: `完成“填写所有个人基础信息”任务，获得积分奖励`

**第 622 行** (string_literal):
```
MissionCompleteContent("50钻石", "完成“填写所有个人基础信息”任务，获得积分奖励", awardType = AwardType.dia) { }
```
中文内容: `50钻石`

**第 622 行** (string_literal):
```
MissionCompleteContent("50钻石", "完成“填写所有个人基础信息”任务，获得积分奖励", awardType = AwardType.dia) { }
```
中文内容: `完成“填写所有个人基础信息”任务，获得积分奖励`

**第 673 行** (string_literal):
```
InputInviteCodeContent("如果你是通过好友推荐下载的本APP，你可以填写好友的邀请码，可立即领取xxx钻石奖励") { }
```
中文内容: `如果你是通过好友推荐下载的本APP，你可以填写好友的邀请码，可立即领取xxx钻石奖励`

**第 758 行** (string_literal):
```
ExchangeContent("积分兑换现金", "确认兑换", centerContent = {
```
中文内容: `积分兑换现金`

**第 758 行** (string_literal):
```
ExchangeContent("积分兑换现金", "确认兑换", centerContent = {
```
中文内容: `确认兑换`

**第 772 行** (string_literal):
```
"12000积分兑换",
```
中文内容: `12000积分兑换`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/japan/boost/SignContent.kt
发现 2 个未本地化的中文字符串:

**第 110 行** (string_literal):
```
contentDescription = "关闭",
```
中文内容: `关闭`

**第 224 行** (string_literal):
```
contentDescription = "已签到",
```
中文内容: `已签到`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/liveroom/HongBaoUI.kt
发现 11 个未本地化的中文字符串:

**第 407 行** (string_literal):
```
Text("可抢用户", color = Color.White.copy(0.8f), modifier = Modifier.align(Alignment.CenterStart))
```
中文内容: `可抢用户`

**第 683 行** (string_literal):
```
"2-50个",
```
中文内容: `2-50个`

**第 684 行** (string_literal):
```
"50钻石起",
```
中文内容: `50钻石起`

**第 685 行** (string_literal):
```
"红包钻石数>999将触发全服飘屏通知",
```
中文内容: `红包钻石数>999将触发全服飘屏通知`

**第 687 行** (string_literal):
```
"未领取的红包过期后会发起退款，2人红包将扣除5%钻石手续费",
```
中文内容: `未领取的红包过期后会发起退款，2人红包将扣除5%钻石手续费`

**第 781 行** (string_literal):
```
"幼儿园的爆红hahahhahaahahhaahhahahaahah的红包",
```
中文内容: `幼儿园的爆红hahahhahaahahhaahhahahaahah的红包`

**第 782 行** (string_literal):
```
"恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,",
```
中文内容: `恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,`

**第 783 行** (string_literal):
```
"本群组成员可抢",
```
中文内容: `本群组成员可抢`

**第 784 行** (string_literal):
```
"红包金额：52钻石",
```
中文内容: `红包金额：52钻石`

**第 785 行** (string_literal):
```
lastTimeDesc = "02:49后开抢",
```
中文内容: `02:49后开抢`

**第 831 行** (string_literal):
```
Text("恭喜，抢到24钻石", fontSize = 18.sp, color = Color(0xFFCA803A))
```
中文内容: `恭喜，抢到24钻石`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/liveroom/RoomBottomLayout.kt
发现 2 个未本地化的中文字符串:

**第 206 行** (string_literal):
```
contentDescription = "礼物",
```
中文内容: `礼物`

**第 282 行** (string_literal):
```
contentDescription = "礼物",
```
中文内容: `礼物`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/liveroom/RoomPassword.kt
发现 2 个未本地化的中文字符串:

**第 254 行** (string_literal):
```
title = "请输入密码",
```
中文内容: `请输入密码`

**第 255 行** (string_literal):
```
buttonText = "确认",
```
中文内容: `确认`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/liveroom/screen/InviteToPrivateRoomScreen.kt
发现 3 个未本地化的中文字符串:

**第 170 行** (string_literal):
```
append("丨")
```
中文内容: `丨`

**第 175 行** (string_literal):
```
append("丨")
```
中文内容: `丨`

**第 282 行** (string_literal):
```
InviteToPrivateRoomScreen(GuideInfo(user = BasicUser.sampleGirl, desc = "她正在找人语音连麦聊天..."), false)
```
中文内容: `她正在找人语音连麦聊天...`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/login/LoginHostScreen.kt
发现 3 个未本地化的中文字符串:

**第 60 行** (string_literal):
```
showToast("该账号未注册, 切换失败")
```
中文内容: `该账号未注册, 切换失败`

**第 89 行** (string_literal):
```
"男$mask",
```
中文内容: `男$mask`

**第 95 行** (string_literal):
```
"女$mask",
```
中文内容: `女$mask`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/login/LoginScreen.kt
发现 1 个未本地化的中文字符串:

**第 132 行** (string_literal):
```
text = "账号管理",
```
中文内容: `账号管理`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/messages/chat/BuddyUI.kt
发现 11 个未本地化的中文字符串:

**第 339 行** (string_literal):
```
BuddyCard("恭喜你们成功结成CP，解锁CP空间", "查看CP空间")
```
中文内容: `恭喜你们成功结成CP，解锁CP空间`

**第 339 行** (string_literal):
```
BuddyCard("恭喜你们成功结成CP，解锁CP空间", "查看CP空间")
```
中文内容: `查看CP空间`

**第 342 行** (string_literal):
```
"对方赠送了CP表白礼物，希望和你结成线上虚拟情侣，邀请24小时后失效",
```
中文内容: `对方赠送了CP表白礼物，希望和你结成线上虚拟情侣，邀请24小时后失效`

**第 343 行** (string_literal):
```
"查看邀请",
```
中文内容: `查看邀请`

**第 351 行** (string_literal):
```
"恋爱君温馨提示：你们关系升级的很快呢！点击下方按钮可与TA结成线上虚拟情侣，解锁更多亲密互动玩法",
```
中文内容: `恋爱君温馨提示：你们关系升级的很快呢！点击下方按钮可与TA结成线上虚拟情侣，解锁更多亲密互动玩法`

**第 352 行** (string_literal):
```
"去组CP",
```
中文内容: `去组CP`

**第 375 行** (string_literal):
```
CPCard(BasicUser.sampleBoy, BasicUser.sampleGirl, "2200", daysDesc = "在一起120天", modifier = Modifier.padding(16.dp))
```
中文内容: `在一起120天`

**第 846 行** (string_literal):
```
"恭喜【无敌小霸王】和【幼儿园搬花】获得1个CP盲盒！",
```
中文内容: `恭喜【无敌小霸王】和【幼儿园搬花】获得1个CP盲盒！`

**第 847 行** (string_literal):
```
"开盲盒送给CP",
```
中文内容: `开盲盒送给CP`

**第 848 行** (string_literal):
```
"999钻石",
```
中文内容: `999钻石`

**第 900 行** (string_literal):
```
append("与CP共同挂麦00:01s后可获得")
```
中文内容: `与CP共同挂麦00:01s后可获得`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/messages/notification/NotificationListItem.kt
发现 9 个未本地化的中文字符串:

**第 175 行** (string_literal):
```
name = "Wakoo官方通知",
```
中文内容: `Wakoo官方通知`

**第 176 行** (string_literal):
```
message = "我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...",
```
中文内容: `我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...`

**第 177 行** (string_literal):
```
time = "1分钟前",
```
中文内容: `1分钟前`

**第 184 行** (string_literal):
```
name = "幼儿园搬花",
```
中文内容: `幼儿园搬花`

**第 185 行** (string_literal):
```
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 186 行** (string_literal):
```
time = "1分钟前",
```
中文内容: `1分钟前`

**第 192 行** (string_literal):
```
name = "幼儿园搬花",
```
中文内容: `幼儿园搬花`

**第 193 行** (string_literal):
```
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 194 行** (string_literal):
```
time = "1分钟前",
```
中文内容: `1分钟前`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/messages/notification/NotificationPage.kt
发现 26 个未本地化的中文字符串:

**第 54 行** (string_literal):
```
name = "Wakoo官方通知",
```
中文内容: `Wakoo官方通知`

**第 55 行** (string_literal):
```
message = "我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...",
```
中文内容: `我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...`

**第 56 行** (string_literal):
```
time = "1分钟前",
```
中文内容: `1分钟前`

**第 63 行** (string_literal):
```
name = "幼儿园搬花",
```
中文内容: `幼儿园搬花`

**第 64 行** (string_literal):
```
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 65 行** (string_literal):
```
time = "1分钟前",
```
中文内容: `1分钟前`

**第 71 行** (string_literal):
```
name = "幼儿园搬花",
```
中文内容: `幼儿园搬花`

**第 72 行** (string_literal):
```
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 73 行** (string_literal):
```
time = "1分钟前",
```
中文内容: `1分钟前`

**第 78 行** (string_literal):
```
name = "幼儿园搬花",
```
中文内容: `幼儿园搬花`

**第 79 行** (string_literal):
```
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 80 行** (string_literal):
```
time = "1分钟前",
```
中文内容: `1分钟前`

**第 85 行** (string_literal):
```
name = "幼儿园搬花",
```
中文内容: `幼儿园搬花`

**第 86 行** (string_literal):
```
message = "我在这里等了好久，你终于出现啦！",
```
中文内容: `我在这里等了好久，你终于出现啦！`

**第 87 行** (string_literal):
```
time = "1分钟前",
```
中文内容: `1分钟前`

**第 98 行** (string_literal):
```
name = "Wakoo官方通知",
```
中文内容: `Wakoo官方通知`

**第 99 行** (string_literal):
```
message = "系统通知：您的账号已激活！",
```
中文内容: `系统通知：您的账号已激活！`

**第 100 行** (string_literal):
```
time = "1分钟前",
```
中文内容: `1分钟前`

**第 106 行** (string_literal):
```
name = "幼儿园搬花",
```
中文内容: `幼儿园搬花`

**第 107 行** (string_literal):
```
message = "关注了你",
```
中文内容: `关注了你`

**第 108 行** (string_literal):
```
time = "10分钟前",
```
中文内容: `10分钟前`

**第 113 行** (string_literal):
```
name = "幼儿园搬花",
```
中文内容: `幼儿园搬花`

**第 114 行** (string_literal):
```
message = "喜欢了你的作品",
```
中文内容: `喜欢了你的作品`

**第 115 行** (string_literal):
```
time = "1小时前",
```
中文内容: `1小时前`

**第 159 行** (string_literal):
```
title = "私信",
```
中文内容: `私信`

**第 169 行** (string_literal):
```
title = "通知",
```
中文内容: `通知`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/profile/ProfileVoiceEditScreen.kt
发现 1 个未本地化的中文字符串:

**第 218 行** (string_literal):
```
message = "删除文件失败",
```
中文内容: `删除文件失败`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/profile/UserProfileUI.kt
发现 1 个未本地化的中文字符串:

**第 935 行** (string_literal):
```
text = "${group.memberCnt}人",
```
中文内容: `${group.memberCnt}人`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/recharge/RechargeRecordScreen.kt
发现 1 个未本地化的中文字符串:

**第 142 行** (string_literal):
```
"充值",
```
中文内容: `充值`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/vip/VipPage.kt
发现 2 个未本地化的中文字符串:

**第 322 行** (string_literal):
```
contentDescription = "用户头像",
```
中文内容: `用户头像`

**第 377 行** (string_literal):
```
contentDescription = "VIP皇冠",
```
中文内容: `VIP皇冠`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/screens/voice/VoicePublishScreen.kt
发现 2 个未本地化的中文字符串:

**第 134 行** (string_literal):
```
""".trimIndent().localizedWithKey("语音发布社区规则")
```
中文内容: `语音发布社区规则`

**第 323 行** (string_literal):
```
message = "删除文件失败",
```
中文内容: `删除文件失败`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/BasicUI.kt
发现 1 个未本地化的中文字符串:

**第 325 行** (string_literal):
```
contentDescription = "返回",
```
中文内容: `返回`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/ButtonUI.kt
发现 5 个未本地化的中文字符串:

**第 383 行** (string_literal):
```
text = "退出登录",
```
中文内容: `退出登录`

**第 389 行** (string_literal):
```
text = "注销账号",
```
中文内容: `注销账号`

**第 395 行** (string_literal):
```
text = "关注她",
```
中文内容: `关注她`

**第 401 行** (string_literal):
```
text = "自定义实心按钮",
```
中文内容: `自定义实心按钮`

**第 409 行** (string_literal):
```
text = "禁用状态",
```
中文内容: `禁用状态`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/ExpandableText.kt
发现 1 个未本地化的中文字符串:

**第 236 行** (string_literal):
```
"在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹。在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹吧..."
```
中文内容: `在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹。在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹吧...`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/QuoteBorderBox.kt
发现 2 个未本地化的中文字符串:

**第 284 行** (string_literal):
```
val shortText = "有時話不多，是因為想得太多。\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。"
```
中文内容: `有時話不多，是因為想得太多。\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。`

**第 286 行** (string_literal):
```
"有時話不多，是因為想得太多。\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。有時話不多，是因為想得太多。喜歡安ंधी的對話，也期待有人能讀懂沉默裡的內心。"
```
中文内容: `有時話不多，是因為想得太多。\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。有時話不多，是因為想得太多。喜歡安ंधी的對話，也期待有人能讀懂沉默裡的內心。`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/TextUI.kt
发现 1 个未本地化的中文字符串:

**第 83 行** (string_literal):
```
SkewedGradientText("关注TA", true)
```
中文内容: `关注TA`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/UserUI.kt
发现 3 个未本地化的中文字符串:

**第 112 行** (string_literal):
```
* 一个简单的文本标签组件，用于显示像 "#唱歌" 这样的内容。
```
中文内容: `#唱歌`

**第 192 行** (string_literal):
```
TextTagChip(text = "#唱歌")
```
中文内容: `#唱歌`

**第 199 行** (string_literal):
```
VoiceTagChip(false, tag = VoiceTag(name = "树洞"))
```
中文内容: `树洞`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/VoicePlayingAnimation.kt
发现 2 个未本地化的中文字符串:

**第 85 行** (string_literal):
```
contentDescription = if (isPlaying) "语音播放中" else "语音已停止",
```
中文内容: `语音播放中`

**第 85 行** (string_literal):
```
contentDescription = if (isPlaying) "语音播放中" else "语音已停止",
```
中文内容: `语音已停止`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/VolumeRippleEffect.kt
发现 2 个未本地化的中文字符串:

**第 198 行** (string_literal):
```
contentDescription = "用户头像",
```
中文内容: `用户头像`

**第 209 行** (string_literal):
```
text = "模拟音量: ${(volume * 100).toInt()}%",
```
中文内容: `模拟音量: ${(volume * 100).toInt()}%`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/gift/GiftPanel.kt
发现 3 个未本地化的中文字符串:

**第 1479 行** (string_literal):
```
add(GiftBean(icon = "", name = "礼物${this.size}", price = 100, id = this.size))
```
中文内容: `礼物${this.size}`

**第 1530 行** (string_literal):
```
add(GiftBean(icon = "", name = "礼物${this.size}", price = 100, id = this.size, superscriptIcon = "asdf"))
```
中文内容: `礼物${this.size}`

**第 1585 行** (string_literal):
```
add(GiftBean(icon = "", name = "礼物${this.size}", price = 100, id = this.size))
```
中文内容: `礼物${this.size}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/gift/GiftWallWidget.kt
发现 8 个未本地化的中文字符串:

**第 588 行** (string_literal):
```
LogUtils.w("GiftWallScreen", "未知的操作purpose")
```
中文内容: `未知的操作purpose`

**第 887 行** (string_literal):
```
gift = GiftWall.GiftWrapper.Gift(t = 0, id = 311, name = "测试礼物"),
```
中文内容: `测试礼物`

**第 897 行** (string_literal):
```
seriesName = "测试礼物$j",
```
中文内容: `测试礼物$j`

**第 914 行** (string_literal):
```
GiftWallSummaryBean.Tab(t = 0, name = "盲盒礼物"),
```
中文内容: `盲盒礼物`

**第 915 行** (string_literal):
```
GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
```
中文内容: `普通礼物`

**第 916 行** (string_literal):
```
GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
```
中文内容: `普通礼物`

**第 917 行** (string_literal):
```
GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
```
中文内容: `普通礼物`

**第 918 行** (string_literal):
```
GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
```
中文内容: `普通礼物`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/gift/YYEVAResourceFetcher.kt
发现 3 个未本地化的中文字符串:

**第 131 行** (string_literal):
```
LogUtils.w("loadImageSync: 加载成功 - $url")
```
中文内容: `loadImageSync: 加载成功 - $url`

**第 136 行** (string_literal):
```
LogUtils.w("loadImageSync: 加载失败 - $url, error: ${result.throwable}")
```
中文内容: `loadImageSync: 加载失败 - $url, error: ${result.throwable}`

**第 142 行** (string_literal):
```
LogUtils.w("loadImageSync: 异常 - $url, error: ${e.message}")
```
中文内容: `loadImageSync: 异常 - $url, error: ${e.message}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/media/data/AndroidMediaRepository.kt
发现 1 个未本地化的中文字符串:

**第 59 行** (string_literal):
```
* 4. 手动创建一个 "所有媒体" 的相册。
```
中文内容: `所有媒体`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/popup/Demo.kt
发现 5 个未本地化的中文字符串:

**第 44 行** (string_literal):
```
contentDescription = "更多选项",
```
中文内容: `更多选项`

**第 70 行** (string_literal):
```
contentDescription = "举报图标",
```
中文内容: `举报图标`

**第 75 行** (string_literal):
```
text = { Text("不感兴趣") },
```
中文内容: `不感兴趣`

**第 85 行** (string_literal):
```
contentDescription = "不感兴趣图标",
```
中文内容: `不感兴趣图标`

**第 99 行** (string_literal):
```
Text("这是下面的内容")
```
中文内容: `这是下面的内容`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/richtext/RichText.kt
发现 9 个未本地化的中文字符串:

**第 327 行** (string_literal):
```
append("欢迎使用优化后的 RichText！")
```
中文内容: `欢迎使用优化后的 RichText！`

**第 329 行** (string_literal):
```
append("现在可以高效处理已知尺寸的组件。")
```
中文内容: `现在可以高效处理已知尺寸的组件。`

**第 339 行** (string_literal):
```
Toast.makeText(context, "疯狂", Toast.LENGTH_SHORT).show()
```
中文内容: `疯狂`

**第 353 行** (string_literal):
```
append("《用户协议》")
```
中文内容: `《用户协议》`

**第 358 行** (string_literal):
```
append("\n例如，这个图标尺寸是固定的: ")
```
中文内容: `\n例如，这个图标尺寸是固定的: `

**第 371 行** (string_literal):
```
append("\n而对于尺寸未知的网络图片: ")
```
中文内容: `\n而对于尺寸未知的网络图片: `

**第 377 行** (string_literal):
```
contentDescription = "小狗",
```
中文内容: `小狗`

**第 380 行** (string_literal):
```
Toast.makeText(context, "大结局", Toast.LENGTH_SHORT).show()
```
中文内容: `大结局`

**第 384 行** (string_literal):
```
append("，它仍然能完美处理。")
```
中文内容: `，它仍然能完美处理。`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/state/StateComponent.kt
发现 1 个未本地化的中文字符串:

**第 308 行** (string_literal):
```
StateComponent.Error(text = "请求错误", errorId = R.drawable.ic_error_for_all) {
```
中文内容: `请求错误`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/DateWheelPicker.kt
发现 3 个未本地化的中文字符串:

**第 521 行** (string_literal):
```
text = String.format("%02d时", hour),
```
中文内容: `%02d时`

**第 548 行** (string_literal):
```
text = String.format("%02d分", minute),
```
中文内容: `%02d分`

**第 576 行** (string_literal):
```
text = String.format("%02d秒", second),
```
中文内容: `%02d秒`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/WheelPickerUtils.kt
发现 19 个未本地化的中文字符串:

**第 192 行** (string_literal):
```
"一月", "二月", "三月", "四月", "五月", "六月",
```
中文内容: `一月`

**第 192 行** (string_literal):
```
"一月", "二月", "三月", "四月", "五月", "六月",
```
中文内容: `二月`

**第 192 行** (string_literal):
```
"一月", "二月", "三月", "四月", "五月", "六月",
```
中文内容: `三月`

**第 192 行** (string_literal):
```
"一月", "二月", "三月", "四月", "五月", "六月",
```
中文内容: `四月`

**第 192 行** (string_literal):
```
"一月", "二月", "三月", "四月", "五月", "六月",
```
中文内容: `五月`

**第 192 行** (string_literal):
```
"一月", "二月", "三月", "四月", "五月", "六月",
```
中文内容: `六月`

**第 193 行** (string_literal):
```
"七月", "八月", "九月", "十月", "十一月", "十二月"
```
中文内容: `七月`

**第 193 行** (string_literal):
```
"七月", "八月", "九月", "十月", "十一月", "十二月"
```
中文内容: `八月`

**第 193 行** (string_literal):
```
"七月", "八月", "九月", "十月", "十一月", "十二月"
```
中文内容: `九月`

**第 193 行** (string_literal):
```
"七月", "八月", "九月", "十月", "十一月", "十二月"
```
中文内容: `十月`

**第 193 行** (string_literal):
```
"七月", "八月", "九月", "十月", "十一月", "十二月"
```
中文内容: `十一月`

**第 193 行** (string_literal):
```
"七月", "八月", "九月", "十月", "十一月", "十二月"
```
中文内容: `十二月`

**第 216 行** (string_literal):
```
"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"
```
中文内容: `星期日`

**第 216 行** (string_literal):
```
"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"
```
中文内容: `星期一`

**第 216 行** (string_literal):
```
"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"
```
中文内容: `星期二`

**第 216 行** (string_literal):
```
"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"
```
中文内容: `星期三`

**第 216 行** (string_literal):
```
"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"
```
中文内容: `星期四`

**第 216 行** (string_literal):
```
"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"
```
中文内容: `星期五`

**第 216 行** (string_literal):
```
"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"
```
中文内容: `星期六`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/demo/EnhancedDatePickerExample.kt
发现 13 个未本地化的中文字符串:

**第 38 行** (string_literal):
```
text = "增强版日期选择器示例",
```
中文内容: `增强版日期选择器示例`

**第 76 行** (string_literal):
```
text = "基础日期选择器",
```
中文内容: `基础日期选择器`

**第 90 行** (string_literal):
```
text = "选中日期: $selectedDate",
```
中文内容: `选中日期: $selectedDate`

**第 116 行** (string_literal):
```
text = "限制到今天的日期选择器",
```
中文内容: `限制到今天的日期选择器`

**第 122 行** (string_literal):
```
text = "只能选择今天及之前的日期",
```
中文内容: `只能选择今天及之前的日期`

**第 137 行** (string_literal):
```
text = "选中日期: $selectedDate",
```
中文内容: `选中日期: $selectedDate`

**第 164 行** (string_literal):
```
text = "循环滚动日期选择器",
```
中文内容: `循环滚动日期选择器`

**第 170 行** (string_literal):
```
text = "月份和日期支持循环滚动，日期固定显示31天（不存在的日期会变灰）",
```
中文内容: `月份和日期支持循环滚动，日期固定显示31天（不存在的日期会变灰）`

**第 187 行** (string_literal):
```
text = "选中日期: $selectedDate",
```
中文内容: `选中日期: $selectedDate`

**第 222 行** (string_literal):
```
text = "自定义范围日期选择器",
```
中文内容: `自定义范围日期选择器`

**第 228 行** (string_literal):
```
text = "只能选择最近30天，月份循环滚动，日期固定31天",
```
中文内容: `只能选择最近30天，月份循环滚动，日期固定31天`

**第 245 行** (string_literal):
```
text = "选中日期: $selectedDate",
```
中文内容: `选中日期: $selectedDate`

**第 251 行** (string_literal):
```
text = "可用范围: ${customDateRange.start} 到 ${customDateRange.endInclusive}",
```
中文内容: `可用范围: ${customDateRange.start} 到 ${customDateRange.endInclusive}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/demo/SimpleWheelPickerTest.kt
发现 13 个未本地化的中文字符串:

**第 30 行** (string_literal):
```
text = "WheelPicker 测试",
```
中文内容: `WheelPicker 测试`

**第 38 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `苹果`

**第 38 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `香蕉`

**第 38 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `橙子`

**第 38 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `葡萄`

**第 38 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `草莓`

**第 38 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `蓝莓`

**第 38 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `芒果`

**第 38 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `菠萝`

**第 63 行** (string_literal):
```
text = "选中项: $selectedItem",
```
中文内容: `选中项: $selectedItem`

**第 71 行** (string_literal):
```
text = "最终索引: ${state.snappedIndex}",
```
中文内容: `最终索引: ${state.snappedIndex}`

**第 76 行** (string_literal):
```
text = "当前索引: ${state.currentIndex}",
```
中文内容: `当前索引: ${state.currentIndex}`

**第 81 行** (string_literal):
```
text = "是否滚动中: ${state.isScrollInProgress}",
```
中文内容: `是否滚动中: ${state.isScrollInProgress}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/demo/WheelPickerEnabledExample.kt
发现 9 个未本地化的中文字符串:

**第 39 行** (string_literal):
```
text = "可用性检查示例",
```
中文内容: `可用性检查示例`

**第 73 行** (string_literal):
```
text = "日期选择器（只能选择今天及之前）",
```
中文内容: `日期选择器（只能选择今天及之前）`

**第 151 行** (string_literal):
```
text = "${year}年",
```
中文内容: `${year}年`

**第 173 行** (string_literal):
```
text = "${month}月",
```
中文内容: `${month}月`

**第 195 行** (string_literal):
```
text = "${day}日",
```
中文内容: `${day}日`

**第 210 行** (string_literal):
```
text = "选中日期: $selectedDate",
```
中文内容: `选中日期: $selectedDate`

**第 234 行** (string_literal):
```
text = "数字选择器（只能选择偶数）",
```
中文内容: `数字选择器（只能选择偶数）`

**第 277 行** (string_literal):
```
text = "选中数字: $selectedNumber",
```
中文内容: `选中数字: $selectedNumber`

**第 283 行** (string_literal):
```
text = "提示: 奇数不可选择，滚动到奇数会自动跳转到最近的偶数",
```
中文内容: `提示: 奇数不可选择，滚动到奇数会自动跳转到最近的偶数`

----------------------------------------

## app/src/main/java/com/buque/wakoo/ui/widget/wheelPicker/demo/WheelPickerExamples.kt
发现 24 个未本地化的中文字符串:

**第 53 行** (string_literal):
```
text = "WheelPicker 示例",
```
中文内容: `WheelPicker 示例`

**第 91 行** (string_literal):
```
text = "基础字符串选择器",
```
中文内容: `基础字符串选择器`

**第 98 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `苹果`

**第 98 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `香蕉`

**第 98 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `橙子`

**第 98 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `葡萄`

**第 98 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `草莓`

**第 98 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `蓝莓`

**第 98 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `芒果`

**第 98 行** (string_literal):
```
listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
```
中文内容: `菠萝`

**第 121 行** (string_literal):
```
text = "选中项: $selectedItem",
```
中文内容: `选中项: $selectedItem`

**第 145 行** (string_literal):
```
text = "数字选择器（无限循环）",
```
中文内容: `数字选择器（无限循环）`

**第 188 行** (string_literal):
```
text = "选中数字: $selectedNumber",
```
中文内容: `选中数字: $selectedNumber`

**第 213 行** (string_literal):
```
text = "日期选择器",
```
中文内容: `日期选择器`

**第 228 行** (string_literal):
```
text = "选中日期: $selectedDate",
```
中文内容: `选中日期: $selectedDate`

**第 254 行** (string_literal):
```
text = "时间选择器",
```
中文内容: `时间选择器`

**第 276 行** (string_literal):
```
text = "选中时间: ${WheelPickerUtils.formatTime(selectedHour, selectedMinute, selectedSecond)}",
```
中文内容: `选中时间: ${WheelPickerUtils.formatTime(selectedHour, selectedMinute, selectedSecond)}`

**第 300 行** (string_literal):
```
text = "自定义样式选择器",
```
中文内容: `自定义样式选择器`

**第 307 行** (string_literal):
```
listOf("小", "中", "大", "特大", "超大")
```
中文内容: `小`

**第 307 行** (string_literal):
```
listOf("小", "中", "大", "特大", "超大")
```
中文内容: `中`

**第 307 行** (string_literal):
```
listOf("小", "中", "大", "特大", "超大")
```
中文内容: `大`

**第 307 行** (string_literal):
```
listOf("小", "中", "大", "特大", "超大")
```
中文内容: `特大`

**第 307 行** (string_literal):
```
listOf("小", "中", "大", "特大", "超大")
```
中文内容: `超大`

**第 359 行** (string_literal):
```
text = "选中尺寸: $selectedItem",
```
中文内容: `选中尺寸: $selectedItem`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/CommonSaver.kt
发现 1 个未本地化的中文字符串:

**第 52 行** (string_literal):
```
*         mutableStateOf(UserInfo("123", "张三"))
```
中文内容: `张三`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/DateTimeUtils.kt
发现 11 个未本地化的中文字符串:

**第 87 行** (string_literal):
```
* 例如："刚刚"、"5分钟前"、"2小时前"等
```
中文内容: `刚刚`

**第 87 行** (string_literal):
```
* 例如："刚刚"、"5分钟前"、"2小时前"等
```
中文内容: `5分钟前`

**第 87 行** (string_literal):
```
* 例如："刚刚"、"5分钟前"、"2小时前"等
```
中文内容: `2小时前`

**第 97 行** (string_literal):
```
diff < 60 -> "刚刚"
```
中文内容: `刚刚`

**第 98 行** (string_literal):
```
diff < 3600 -> "${diff / 60}分钟前"
```
中文内容: `${diff / 60}分钟前`

**第 99 行** (string_literal):
```
diff < 86400 -> "${diff / 3600}小时前"
```
中文内容: `${diff / 3600}小时前`

**第 100 行** (string_literal):
```
diff < 86400 * 2 -> "昨天"
```
中文内容: `昨天`

**第 101 行** (string_literal):
```
diff < 86400 * 7 -> "${diff / 86400}天前"
```
中文内容: `${diff / 86400}天前`

**第 197 行** (string_literal):
```
hours > 0 -> String.format("%d小时%d分钟%d秒", hours, minutes, remainingSeconds)
```
中文内容: `%d小时%d分钟%d秒`

**第 198 行** (string_literal):
```
minutes > 0 -> String.format("%d分钟%d秒", minutes, remainingSeconds)
```
中文内容: `%d分钟%d秒`

**第 199 行** (string_literal):
```
else -> String.format("%d秒", remainingSeconds)
```
中文内容: `%d秒`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/FileUtils.kt
发现 1 个未本地化的中文字符串:

**第 113 行** (string_literal):
```
throw IOException("无法创建录音目录: ${dir.absolutePath}")
```
中文内容: `无法创建录音目录: ${dir.absolutePath}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/LogUtils.kt
发现 3 个未本地化的中文字符串:

**第 212 行** (string_literal):
```
d("JSON空字符串", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)
```
中文内容: `JSON空字符串`

**第 220 行** (string_literal):
```
e(e, "JSON格式化失败: $json", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)
```
中文内容: `JSON格式化失败: $json`

**第 238 行** (string_literal):
```
eTag(tag, e, "JSON格式化失败: $json", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)
```
中文内容: `JSON格式化失败: $json`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/PermissionUtils.kt
发现 6 个未本地化的中文字符串:

**第 165 行** (string_literal):
```
Manifest.permission.RECORD_AUDIO -> "录音权限：用于录制语音内容"
```
中文内容: `录音权限：用于录制语音内容`

**第 166 行** (string_literal):
```
Manifest.permission.READ_EXTERNAL_STORAGE -> "存储读取权限：用于读取音频文件"
```
中文内容: `存储读取权限：用于读取音频文件`

**第 167 行** (string_literal):
```
Manifest.permission.WRITE_EXTERNAL_STORAGE -> "存储写入权限：用于保存录音文件"
```
中文内容: `存储写入权限：用于保存录音文件`

**第 168 行** (string_literal):
```
Manifest.permission.READ_MEDIA_AUDIO -> "媒体音频权限：用于访问音频文件"
```
中文内容: `媒体音频权限：用于访问音频文件`

**第 169 行** (string_literal):
```
else -> "未知权限：$permission"
```
中文内容: `未知权限：$permission`

**第 176 行** (multiline_string):
```
"""多行字符串 (第176行)"""
```
中文内容: `录音功能需要以下权限：
        
        • 录音权限：用于录制语音内容
        • 存储权限：用于保存和读取录音文件
        
        请授予这些权限以正常使用录音功能。`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/datapoints/ShushuUtils.kt
发现 4 个未本地化的中文字符串:

**第 103 行** (string_literal):
```
newProperties.put("gender", if (it.gender == 1) "男" else "女")
```
中文内容: `男`

**第 103 行** (string_literal):
```
newProperties.put("gender", if (it.gender == 1) "男" else "女")
```
中文内容: `女`

**第 118 行** (string_literal):
```
"gender" to if (it.gender == 1) "男" else "女",
```
中文内容: `男`

**第 118 行** (string_literal):
```
"gender" to if (it.gender == 1) "男" else "女",
```
中文内容: `女`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/koru/sample.kt
发现 22 个未本地化的中文字符串:

**第 24 行** (string_literal):
```
is BrokerEvent.TaskSubmitted -> "✅ [Broker] 任务已提交: ID=${event.taskId}, 优先级=${event.task.priority}"
```
中文内容: `✅ [Broker] 任务已提交: ID=${event.taskId}, 优先级=${event.task.priority}`

**第 25 行** (string_literal):
```
is BrokerEvent.TaskRetracted -> "↩️ [Broker] 任务已撤回: ID=${event.taskId}"
```
中文内容: `↩️ [Broker] 任务已撤回: ID=${event.taskId}`

**第 26 行** (string_literal):
```
is BrokerEvent.TaskPriorityUpdated -> "🔼 [Broker] 优先级更新: ID=${event.taskId}, 新优先级=${event.newPriority}"
```
中文内容: `🔼 [Broker] 优先级更新: ID=${event.taskId}, 新优先级=${event.newPriority}`

**第 27 行** (string_literal):
```
is BrokerEvent.TaskDispatched -> "🚚 [Broker] 任务已分发: ID=${event.taskId} -> Consumer [${event.consumerId}]"
```
中文内容: `🚚 [Broker] 任务已分发: ID=${event.taskId} -> Consumer [${event.consumerId}]`

**第 28 行** (string_literal):
```
is BrokerEvent.TaskExecutionStarted -> "▶️ [Consumer] [${event.consumerId}] 开始执行任务: ID=${event.taskId}"
```
中文内容: `▶️ [Consumer] [${event.consumerId}] 开始执行任务: ID=${event.taskId}`

**第 29 行** (string_literal):
```
is BrokerEvent.TaskExecutionSuccess -> "✔️ [Consumer] [${event.consumerId}] 成功完成任务: ID=${event.taskId}"
```
中文内容: `✔️ [Consumer] [${event.consumerId}] 成功完成任务: ID=${event.taskId}`

**第 30 行** (string_literal):
```
is BrokerEvent.TaskExecutionFailed -> "❌ [Consumer] [${event.consumerId}] 任务失败: ID=${event.taskId}, 原因: ${event.error.message}"
```
中文内容: `❌ [Consumer] [${event.consumerId}] 任务失败: ID=${event.taskId}, 原因: ${event.error.message}`

**第 31 行** (string_literal):
```
is BrokerEvent.TaskExecutionCancelled -> "❌ [Consumer] [${event.consumerId}] 任务取消: ID=${event.taskId}"
```
中文内容: `❌ [Consumer] [${event.consumerId}] 任务取消: ID=${event.taskId}`

**第 32 行** (string_literal):
```
is BrokerEvent.QueueCleared -> "🗑️ [Broker] 任务队列已清空"
```
中文内容: `🗑️ [Broker] 任务队列已清空`

**第 33 行** (string_literal):
```
is BrokerEvent.BrokerShutdown -> "🛑 [Broker] 调度中心已关闭"
```
中文内容: `🛑 [Broker] 调度中心已关闭`

**第 49 行** (string_literal):
```
broker.submit(createTask("普通邮件", 5, 1000))
```
中文内容: `普通邮件`

**第 50 行** (string_literal):
```
broker.submit(createTask("报告生成", 2, 2000))
```
中文内容: `报告生成`

**第 51 行** (string_literal):
```
broker.submit(createTask("!!!紧急修复!!!", 10, 1500))
```
中文内容: `!!!紧急修复!!!`

**第 57 行** (string_literal):
```
PayloadTask(id = lowPriorityTaskId, "低优先级任务", 1, onAction = { payload ->
```
中文内容: `低优先级任务`

**第 62 行** (string_literal):
```
broker.submit(createTask("另一个普通任务", 5, 1000))
```
中文内容: `另一个普通任务`

**第 70 行** (string_literal):
```
broker.submit(createTask("会被消费的任务", 5, 1000))
```
中文内容: `会被消费的任务`

**第 74 行** (string_literal):
```
"这个任务将被撤回",
```
中文内容: `这个任务将被撤回`

**第 86 行** (string_literal):
```
broker.submit(createTask("任务A-1 (给A)", 7, 1000))
```
中文内容: `任务A-1 (给A)`

**第 87 行** (string_literal):
```
broker.submit(createTask("任务C-1 (给B)", 8, 1000))
```
中文内容: `任务C-1 (给B)`

**第 95 行** (string_literal):
```
PayloadTask<String>(payload = "会失败的任务", priority = 9, onError = { e ->
```
中文内容: `会失败的任务`

**第 97 行** (string_literal):
```
}) { throw IllegalStateException("数据库连接失败") }
```
中文内容: `数据库连接失败`

**第 100 行** (string_literal):
```
val longRunningTask = createTask("一个很长的任务", 8, 10000)
```
中文内容: `一个很长的任务`

----------------------------------------

## app/src/main/java/com/buque/wakoo/utils/ninepatch/NinePathLoader.kt
发现 1 个未本地化的中文字符串:

**第 46 行** (string_literal):
```
LogUtils.i("DotNinePng", "提前预加载点九bitmap: ${cacheSet.size}")
```
中文内容: `提前预加载点九bitmap: ${cacheSet.size}`

----------------------------------------

## app/src/main/java/com/buque/wakoo/viewmodel/GiftWallDetailViewModel.kt
发现 1 个未本地化的中文字符串:

**第 132 行** (string_literal):
```
return Result.failure(Exception("无法施舍礼物,因为begId = null"))
```
中文内容: `无法施舍礼物,因为begId = null`

----------------------------------------

