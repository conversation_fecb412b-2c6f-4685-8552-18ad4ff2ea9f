{"strings_by_file": {"app/src/main/java/com/buque/wakoo/bean/LiveRoom.kt": {"high_confidence": [{"context": "title = \"你要约会吗?\",", "line": 81, "text": "你要约会吗?"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/bean/RechargeData.kt": {"high_confidence": [{"context": "val description: String, // 描述，如\"充值钻石\"", "line": 78, "text": "充值钻石"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/bean/VoiceData.kt": {"high_confidence": [{"context": "title = \"有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話\",", "line": 55, "text": "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話"}, {"context": "title = \"有時話不多\",", "line": 151, "text": "有時話不多"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/manager/AudioRecordManager.kt": {"high_confidence": [{"context": "\"释放录制器失败\",", "line": 729, "text": "释放录制器失败"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/screens/dressup/SilverShopListScreen.kt": {"high_confidence": [{"context": "text = \"总计需要消耗银币：%s\\n兑换成功后将发放至礼物背包\".localizedFormatWithKey(\"总计需要消耗银币\", totalPrice),", "line": 691, "text": "总计需要消耗银币"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/screens/japan/boost/SignContent.kt": {"high_confidence": [{"context": "contentDescription = \"关闭\",", "line": 110, "text": "关闭"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/screens/liveroom/HongBaoUI.kt": {"high_confidence": [{"context": "Text(\"可抢用户\", color = Color.White.copy(0.8f), modifier = Modifier.align(Alignment.CenterStart))", "line": 407, "text": "可抢用户"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/screens/login/LoginHostScreen.kt": {"high_confidence": [{"context": "showToast(\"该账号未注册, 切换失败\")", "line": 60, "text": "该账号未注册, 切换失败"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/screens/login/LoginScreen.kt": {"high_confidence": [{"context": "text = \"账号管理\",", "line": 132, "text": "账号管理"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/screens/messages/notification/NotificationPage.kt": {"high_confidence": [{"context": "message = \"我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...\",", "line": 55, "text": "我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦..."}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 64, "text": "我在这里等了好久，你终于出现啦！"}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 72, "text": "我在这里等了好久，你终于出现啦！"}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 79, "text": "我在这里等了好久，你终于出现啦！"}, {"context": "message = \"我在这里等了好久，你终于出现啦！\",", "line": 86, "text": "我在这里等了好久，你终于出现啦！"}, {"context": "message = \"系统通知：您的账号已激活！\",", "line": 99, "text": "系统通知：您的账号已激活！"}, {"context": "message = \"关注了你\",", "line": 107, "text": "关注了你"}, {"context": "message = \"喜欢了你的作品\",", "line": 114, "text": "喜欢了你的作品"}, {"context": "title = \"私信\",", "line": 159, "text": "私信"}, {"context": "title = \"通知\",", "line": 169, "text": "通知"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/screens/profile/ProfileVoiceEditScreen.kt": {"high_confidence": [{"context": "message = \"删除文件失败\",", "line": 218, "text": "删除文件失败"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/screens/recharge/RechargeRecordScreen.kt": {"high_confidence": [{"context": "\"充值\",", "line": 142, "text": "充值"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/screens/voice/VoicePublishScreen.kt": {"high_confidence": [{"context": "message = \"删除文件失败\",", "line": 323, "text": "删除文件失败"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/ui/widget/BasicUI.kt": {"high_confidence": [{"context": "contentDescription = \"返回\",", "line": 325, "text": "返回"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/utils/LogUtils.kt": {"high_confidence": [{"context": "e(e, \"JSON格式化失败: $json\", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)", "line": 220, "text": "JSON格式化失败: $json"}, {"context": "eTag(tag, e, \"JSON格式化失败: $json\", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)", "line": 238, "text": "JSON格式化失败: $json"}], "medium_confidence": []}, "app/src/main/java/com/buque/wakoo/utils/PermissionUtils.kt": {"high_confidence": [{"context": "Manifest.permission.WRITE_EXTERNAL_STORAGE -> \"存储写入权限：用于保存录音文件\"", "line": 167, "text": "存储写入权限：用于保存录音文件"}], "medium_confidence": []}}, "summary": {"excluded_content": ["预览函数 (@Preview)", "日志语句 (Log.*, LogUtils.*, println等)", "测试和调试代码", "数据模型示例数据", "注释内容"], "high_confidence": 27, "medium_confidence": 0, "scan_directories": ["app/src/main/java"], "total_files": 16, "total_strings": 27}}