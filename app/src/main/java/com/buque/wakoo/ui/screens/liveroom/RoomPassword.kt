package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalTextToolbar
import androidx.compose.ui.platform.TextToolbar
import androidx.compose.ui.platform.TextToolbarStatus
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.CenterDialog
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.icons.GreenChecked
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppPullToRefreshBox
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.CStateLayout

class SetRoomPasswordDialog : CenterDialog<LiveRoomInfoState>() {
    override val isRestorable: Boolean = false

    @Composable
    override fun DialogScope.Content(param: LiveRoomInfoState) {
        var confirmPwd by rememberSaveable {
            mutableStateOf("")
        }
        LiveRoomPasswordInput(
            title = "锁定房间后需要输入密码才能进入".localized,
            buttonText = if (confirmPwd.isNotEmpty()) "确认密码".localized else "请设置密码".localized,
            refreshInputPwd = confirmPwd,
            onConfirm = {
                if (confirmPwd.isNotEmpty()) {
                    if (confirmPwd != it) {
                        showToast("密码不一致".localized)
                    } else {
                        // TODO: 锁房
                        showToast("锁房成功".localized)
                        dismiss()
                    }
                } else {
                    confirmPwd = it
                    showToast("再次输入密码确认".localized)
                }
            },
        )
    }
}

class ValidateRoomPasswordDialog : CenterDialog<LiveRoomInfoState>() {
    override val isRestorable: Boolean = false

    @Composable
    override fun DialogScope.Content(param: LiveRoomInfoState) {
        LiveRoomPasswordInput(
            title = "该房间需要输入密码才能进入".localized,
            buttonText = "确认密码".localized,
            onConfirm = {
                // TODO: 校验密码
                showToast("密码错误".localized)
                dismiss()
            },
        )
    }
}

@Composable
private fun LiveRoomPasswordInput(
    title: String,
    buttonText: String,
    modifier: Modifier = Modifier,
    passwordLength: Int = 4,
    refreshInputPwd: Any = Unit,
    onConfirm: (String) -> Unit,
) {
    val focusRequester = remember { FocusRequester() }
    var passwordValue by rememberSaveable(refreshInputPwd) {
        mutableStateOf("")
    }
    val cursorIndex by remember(passwordLength) {
        derivedStateOf {
            passwordValue.length
        }
    }

    Column(
        modifier =
            modifier
                .padding(horizontal = 26.dp)
                .fillMaxWidth()
                .background(WakooWhite, RoundedCornerShape(12.dp))
                .padding(20.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = title,
            color = WakooText,
            style = MaterialTheme.typography.bodyMedium,
        )
        Box {
            CompositionLocalProvider(LocalTextToolbar provides EmptyTextToolbar) {
                BasicTextField(
                    value = passwordValue,
                    onValueChange = { newText ->
                        val filteredText = newText.filter { it.isDigit() }
                        if (filteredText.length <= passwordLength) {
                            passwordValue = filteredText
                        }
                    },
                    modifier =
                        Modifier
                            .matchParentSize()
                            .alpha(0f)
                            .focusRequester(focusRequester),
                    singleLine = true,
                    keyboardOptions =
                        KeyboardOptions(
                            keyboardType = KeyboardType.Number,
                            imeAction = ImeAction.Done,
                        ),
                    keyboardActions =
                        KeyboardActions(
                            onDone = {
                                if (passwordValue.length >= passwordLength) {
                                    onConfirm(passwordValue)
                                }
                            },
                        ),
                )
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally),
            ) {
                repeat(passwordLength) { index ->
                    Row(
                        modifier =
                            Modifier
                                .requiredWidthIn(max = 40.dp)
                                .weight(1f)
                                .aspectRatio(1f)
                                .background(Color(0xFFE9EAEF), RoundedCornerShape(15)),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterHorizontally),
                    ) {
                        val text by remember {
                            derivedStateOf {
                                passwordValue.getOrNull(index)?.toString().orEmpty()
                            }
                        }
                        if (text.isNotEmpty()) {
                            Text(
                                text = passwordValue.getOrNull(index)?.toString().orEmpty(),
                                style =
                                    MaterialTheme.typography.titleSmall.merge(
                                        color = Color(0xFF111111),
                                        textAlign = TextAlign.Center,
                                        fontWeight = FontWeight.Medium,
                                    ),
                            )
                        }

                        if (cursorIndex == index) {
                            BlinkingCursor()
                        }
                    }
                }
            }
        }
        SolidButton(
            text = buttonText,
            onClick = { onConfirm(passwordValue) },
            fontSize = 14.sp,
            height = 40.dp,
            enabled = passwordValue.length >= passwordLength,
            paddingValues = PaddingValues(horizontal = 30.dp),
        )
    }

    LaunchedEffect(focusRequester) {
        focusRequester.requestFocus()
    }
}

@Preview
@Composable
private fun PreviewLiveRoomPasswordInput() {
    WakooTheme {
        Dialog(onDismissRequest = {}) {
            LiveRoomPasswordInput(
                title = "请输入密码",
                buttonText = "确认",
                onConfirm = {},
            )
        }
    }
}

/**
 * 一个模拟输入法光标的组件，具有闪烁效果。
 *
 * @param modifier 修饰符，用于自定义布局。
 * @param width 光标的宽度。
 * @param height 光标的高度，通常应与相邻文本的字体大小匹配。
 * @param cursorColor 光标的颜色。
 * @param blinkingDurationMillis 闪烁一次的完整时长（从亮到暗再到亮）。
 */
@Composable
private fun BlinkingCursor(
    modifier: Modifier = Modifier,
    width: Dp = 2.dp,
    height: Dp = 20.dp,
    cursorColor: Color = MaterialTheme.colorScheme.primary,
    blinkingDurationMillis: Int = 1000, // 标准光标闪烁周期大约是1秒
) {
    // 1. 创建一个无限循环的过渡动画
    val infiniteTransition = rememberInfiniteTransition(label = "cursor-blink")

    // 2. 在过渡中定义一个浮点数动画，用于控制 alpha (透明度)
    //    动画从 1.0f (不透明) -> 0.0f (透明) 循环
    val alpha by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 0f,
        animationSpec =
            infiniteRepeatable(
                // 动画时长为总周期的一半，因为它会反向播放
                animation = tween(durationMillis = blinkingDurationMillis / 2),
                repeatMode = RepeatMode.Reverse, // 反向重复，实现 1 -> 0 -> 1 的效果
            ),
        label = "cursor-alpha",
    )

    // 3. 绘制一个 Box 作为光标，并应用动画化的 alpha 值
    Box(
        modifier =
            modifier
                .width(width)
                .height(height)
                .background(cursorColor.copy(alpha = alpha)),
    )
}

// 1. 创建一个空的 TextToolbar 实现
object EmptyTextToolbar : TextToolbar {
    override val status: TextToolbarStatus = TextToolbarStatus.Hidden

    override fun showMenu(
        rect: Rect,
        onCopyRequested: (() -> Unit)?,
        onPasteRequested: (() -> Unit)?,
        onCutRequested: (() -> Unit)?,
        onSelectAllRequested: (() -> Unit)?,
    ) {
        // 什么都不做，菜单就不会显示
    }

    override fun hide() {
        // 什么都不做
    }
}

@Composable
fun Xx(modifier: Modifier = Modifier) {
    val cState = CState.Success(1)
    var selectedId by rememberSaveable {
        mutableStateOf("")
    }
    TitleScreenScaffold(
        title = "房间背景设置".localized,
    ) {
        AppPullToRefreshBox(
            isRefreshing = cState.isRefreshing,
            onRefresh = {
            },
            modifier =
                Modifier
                    .padding(it)
                    .fillMaxSize(),
        ) {
            CStateLayout(
                state = cState,
                emptyCheckProvider = { false },
                onRetry = {
                },
            ) { data ->
                Box(modifier = Modifier.fillMaxSize()) {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(3),
                        modifier = Modifier.fillMaxSize(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        contentPadding = PaddingValues(start = 16.dp, end = 16.dp, bottom = 112.dp),
                    ) {
                        items(10) {
                            Column {
                                val isSelected by remember {
                                    derivedStateOf {
                                        selectedId == "123"
                                    }
                                }

                                Box(
                                    modifier =
                                        modifier
                                            .fillMaxWidth()
                                            .aspectRatio(0.589f)
                                            .clip(RoundedCornerShape(8.dp))
                                            .clickable(onClick = {})
                                            .border(
                                                width = if (isSelected) 1.5.dp else 0.dp,
                                                color = if (isSelected) Color(0xFF66FE6B) else Color.Transparent,
                                                shape = RoundedCornerShape(8.dp),
                                            ),
                                    contentAlignment = Alignment.BottomEnd,
                                ) {
                                    NetworkImage(
                                        data = "",
                                        modifier = Modifier.fillMaxSize(),
                                    )
                                    if (isSelected) {
                                        Image(
                                            imageVector = WakooIcons.GreenChecked,
                                            contentDescription = null,
                                            modifier = Modifier.size(20.dp),
                                        )
                                    }
                                }
                                SizeHeight(8.dp)
                                Text(
                                    text = "123",
                                    style =
                                        MaterialTheme.typography.bodyMedium
                                            .copy(color = WakooText, fontWeight = FontWeight.Medium),
                                )
                            }
                        }
                    }

                    Box(
                        modifier =
                            Modifier
                                .align(Alignment.BottomCenter)
                                .fillMaxWidth()
                                .background(
                                    Brush.verticalGradient(
                                        listOf(
                                            Color(0x00FFFFFF),
                                            Color(0x99FFFFFF),
                                            Color(0xFFFFFFFF),
                                            Color(0xFFFFFFFF),
                                        ),
                                    ),
                                ).padding(horizontal = 28.dp, vertical = 26.dp),
                    ) {
                        GradientButton(
                            text = "确认修改".localized,
                            modifier = Modifier.fillMaxWidth(),
                            onClick = {
                            },
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewXx() {
    WakooTheme {
        Xx()
    }
}
