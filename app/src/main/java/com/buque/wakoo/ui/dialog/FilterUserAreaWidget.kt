package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.composeClick
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.VipIcon
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.image.NetworkImage

@Composable
fun FilterUserAreaWidget(
    selectIdx: Int = 0,
    onSelected: (selectIdx: Int) -> Unit = {},
) {
    var current by remember {
        mutableStateOf(selectIdx)
    }
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(Color.White, RoundedCornerShape(bottomStart = 12.dp, bottomEnd = 12.dp))
                .padding(horizontal = 16.dp)
                .padding(bottom = 16.dp)
                .statusBarsPadding(),
    ) {
        Text(
            "选择你想看到的用户".localized,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .heightIn(44.dp),
            textAlign = TextAlign.Center,
            color = WakooText,
            fontSize = 16.sp,
            lineHeight = 44.sp,
            fontWeight = FontWeight.Medium,
        )
        Spacer(modifier = Modifier.height(16.dp))
        Row(verticalAlignment = Alignment.Bottom) {
            Text(
                "所有地区用户".localized,
                modifier =
                    Modifier
                        .weight(1f)
                        .background(
                            color = if (current == 0) WakooText else Color.White,
                            shape = CircleShape,
                        ).border(2.dp, if (current == 0) Color.Transparent else WakooText, CircleShape)
                        .clip(CircleShape)
                        .height(32.dp)
                        .click { current = 0 },
                textAlign = TextAlign.Center,
                color = if (current == 0) Color.White else WakooText,
                fontSize = 12.sp,
                lineHeight = 32.sp,
            )
            Spacer(modifier = Modifier.width(16.dp))
            Box(
                modifier =
                    Modifier
                        .weight(1f)
                        .height(32.dp)
                        .click { current = 1 },
            ) {
                Text(
                    "仅同城用户".localized,
                    modifier =
                        Modifier
                            .fillMaxSize()
                            .background(
                                color = if (current == 1) WakooText else Color.White,
                                shape = CircleShape,
                            ).border(2.dp, if (current == 1) Color.Transparent else WakooText, CircleShape)
                            .clip(CircleShape)
                            .click { current = 1 },
                    textAlign = TextAlign.Center,
                    color = if (current == 1) Color.White else WakooText,
                    fontSize = 12.sp,
                    lineHeight = 32.sp,
                )
                Image(
                    WakooIcons.VipIcon,
                    contentDescription = null,
                    modifier =
                        Modifier
                            .align(Alignment.TopEnd)
                            .offset(y = -6.dp),
                )
            }
        }
        SizeHeight(28.dp)
        GradientButton(
            text = "开始筛选",
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(48.dp),
            onClick =
                composeClick {
                    onSelected(current)
                },
        )
    }
}
