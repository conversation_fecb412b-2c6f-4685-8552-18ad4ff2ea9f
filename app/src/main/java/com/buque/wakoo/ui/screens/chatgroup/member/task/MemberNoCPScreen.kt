package com.buque.wakoo.ui.screens.chatgroup.member.task

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.UserLevelWidget
import com.buque.wakoo.ui.widget.VipTag
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout

@Composable
fun MemberNoCPScreen() {
    val title = "群组内从未组过CP的成员".localized
    val vm: TaskMemberListViewModel = viewModel()
    val count by vm.stateOfCount
    TitleScreenScaffold(title) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
        ) {

            StateListPaginateLayout<Int, UserResponse, TaskMemberListViewModel>(viewModel = vm) { state, data ->
                LazyColumn(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.spacedBy(16.dp)) {
                    item {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(44.dp)
                                .padding(horizontal = 16.dp), verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(title, fontSize = 12.sp, color = WakooGrayText)
                            SizeWidth(4.dp)
                            Text("%d人".localizedFormat(count), fontSize = 12.sp, color = Color(0xFFFF385C))
                        }
                    }
                    items(data) { item ->
                        GroupMemberItem(item, modifier = Modifier.padding(horizontal = 16.dp))
                    }
                }
            }
        }
    }
}


@Composable
private fun GroupMemberItem(item: UserResponse, modifier: Modifier = Modifier) {
    val nav = LocalAppNavController.root
    Row(modifier = modifier) {
        Box(modifier = Modifier.size(48.dp)) {
            AvatarNetworkImage(
                modifier =
                    Modifier.noEffectClick(onClick = {
                        nav.push(Route.UserProfile(item.basicUser))
                    }),
                user = item.basicUser,
                size = 48.dp,
            )
            if (item.onlineStatus == 0) {
                Box(
                    modifier =
                        Modifier
                            .size(14.dp)
                            .align(Alignment.BottomEnd)
                            .padding(2.dp)
                            .background(Color.White, CircleShape)
                            .padding(2.dp)
                            .background(
                                WakooGreen,
                                CircleShape,
                            ),
                )
            }
        }
        SizeWidth(8.dp)
        Column {
            Text(
                text = item.basicUser.name,
                color = Color(0xFF111111),
                style = MaterialTheme.typography.labelMedium,
                textAlign = TextAlign.Center,
                maxLines = 1,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                overflow = TextOverflow.Ellipsis,
            )
            SizeHeight(8.dp)
            Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.spacedBy(4.dp)) {
                GenderAgeTag(item.basicUser)
                if (item.isMember) {
                    VipTag()
                }
                UserLevelWidget(item.level)
            }
        }
    }

}