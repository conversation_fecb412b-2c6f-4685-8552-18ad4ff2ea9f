package com.buque.wakoo.game.core

/**
 * 游戏元素属性接口
 *
 * 定义了游戏中所有元素必须实现的基本属性：
 * @property id 元素的唯一标识符
 * @property name 元素的显示名称
 * @property desc 元素的描述文本
 * @property exclusiveAttributeIds 元素具有的属性列表，如可食用、有重力等
 */
sealed interface Attributes {
    val id: String
    val name: String
    val desc: String
    val coreVersion: Int get() = 1
    val exclusiveAttributeIds: List<String> get() = emptyList()

    /** 重力属性 */
    data object Gravity : Attributes {
        override val id: String = "gravity"
        override val name: String = "重力属性"
        override val desc: String = "脚下必须有支撑点才不会下落"
    }

    /** 可食用属性 */
    data class Edible(
        val value: Int = 1,
    ) : Attributes {
        override val id: String = "edible"
        override val name: String = "可食用属性"
        override val desc: String = "可以被小蛇吃掉，改变蛇的长度"
    }

    /** 致命属性 */
    data object Deadly : Attributes {
        override val id: String = "deadly"
        override val name: String = "致命属性"
        override val desc: String = "接触后小蛇死亡，游戏结束"
    }

    /** 支撑点属性 */
    data object Support : Attributes {
        override val id: String = "support"
        override val name: String = "支撑点属性"
        override val desc: String = "可以支撑有重力属性的元素不下落"
    }

    /** 可推动属性 */
    data object Pushable : Attributes {
        override val id: String = "pushable"
        override val name: String = "可推动属性"
        override val desc: String = "可以被其他可移动元素推动"
    }

    /** 胜利属性 */
    data object Victory : Attributes {
        override val id: String = "victory"
        override val name: String = "胜利属性"
        override val desc: String = "小蛇头部接触后通关"
    }
}
