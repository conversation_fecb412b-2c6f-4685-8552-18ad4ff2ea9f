package com.buque.wakoo.game.core
/**
 * 移动方向枚举
 * 包含上下左右四个基本方向
 */
enum class Direction {
    Up, // 上
    Down, // 下
    Left, // 左
    Right, // 右
}

/**
 * 蛇的部位枚举
 */
enum class SnakePart {
    Head, // 蛇头
    Body, // 蛇身体
    Tail, // 蛇尾
    None, // 无
}

/**
 * 二维坐标点
 * @property y 纵坐标
 * @property x 横坐标
 */
data class Position(
    val y: Int,
    val x: Int,
) {
    /**
     * 坐标点相加运算符重载
     * @param other 另一个坐标点
     * @return 相加后的新坐标点
     */
    infix operator fun plus(other: Position) = Position(y + other.y, x + other.x)

    /**
     * 坐标点与整数相乘运算符重载
     * @param other 乘数
     * @return 相乘后的新坐标点
     */
    infix operator fun times(other: Int) = Position(y * other, x * other)
}

/**
 * 游戏操作结果枚举
 * - Success: 操作成功且继续游戏
 * - Death: 角色死亡，游戏失败
 * - Victory: 到达终点，游戏胜利
 * - Forbidden: 非法操作，不允许执行
 */
enum class ActionResult {
    Success,
    Death,
    Victory,
    Forbidden,
}

/**
 * 游戏通关解法
 * @property moves 移动方向列表
 * @property steps 移动步数
 */
data class Solution(
    val moves: List<Direction>,
    val steps: Int,
)