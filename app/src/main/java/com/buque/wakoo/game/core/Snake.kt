package com.buque.wakoo.game.core

import kotlin.math.abs

/**
 * 蛇的数据类，包含蛇的所有状态信息
 * @param segments 蛇身体段的位置列表
 * @param direction 蛇头当前的朝向
 * @param targetLength 蛇的目标长度（可能与当前实际长度不同）
 */
class Snake(
    private val segments: MutableList<Position>,
    private var direction: Direction,
) {
    private var targetLength: Int = segments.size

    // 对外暴露蛇身体段的只读列表
    val bodySegments: List<Position>
        get() = segments

    // 对外暴露蛇头的朝向
    val headDirection: Direction
        get() = direction

    /**
     * 验证蛇的初始状态是否合法
     * 主要检查以下几个方面:
     * 1. 蛇的长度是否至少为1
     * 2. 蛇身是否保持连通性(每一节都与其相邻节是连通的)
     * 3. 从蛇头开始是否存在有效的哈密顿路径
     * 4. 蛇头的朝向是否合理(不能对着第二节身体，否则看上去怪异)
     *
     * @throws IllegalStateException 当蛇的状态不合法时抛出异常，异常信息会说明具体原因:
     * - 蛇太短了，至少要有一节身体
     * - 蛇的身体断开了，每一节都要连在一起
     * - 蛇头的位置不对，找不到一条可以走完全身的路径
     * - 蛇头正对着自己的身体，这样会撞到自己
     */
    @Throws(IllegalStateException::class)
    fun validateSnake() {
        // 检查蛇的最小长度要求
        check(segments.isNotEmpty()) {
            "蛇太短了，至少要有一节身体"
        }

        // 如果蛇长大于1，检查蛇身的连通性和重叠
        if (segments.size > 1) {
            // 检查重叠
            val uniquePositions = segments.toSet()
            check(uniquePositions.size == segments.size) { "蛇的身体出现重叠" }

            // 检查相邻节点的连通性
            for (i in 0 until segments.size - 1) {
                val current = segments[i]
                val next = segments[i + 1]
                val isConnected =
                    (abs(next.x - current.x) == 1 && next.y == current.y) ||
                        (abs(next.y - current.y) == 1 && next.x == current.x)
                check(isConnected) { "蛇的身体断开了，每一节都要连在一起" }
            }
        }

        // 获取蛇头位置并验证是否存在有效的哈密顿路径
        val head = segments[0]
        val validHead = findHamiltonianPath(head)
        check(validHead) { "蛇头的位置不对，找不到一条可以走完全身的路径" }

        // 如果蛇长大于1，检查蛇头方向是否合理
        if (segments.size > 1) {
            val neck = segments[1] // 第二节(脖子)的位置
            // 根据蛇头和脖子的相对位置判断不合法的方向
            val invalidDirection =
                when {
                    head.x < neck.x -> direction == Direction.Down // 头在上，不能向下
                    head.x > neck.x -> direction == Direction.Up // 头在下，不能向上
                    head.y < neck.y -> direction == Direction.Right // 头在左，不能向右
                    head.y > neck.y -> direction == Direction.Left // 头在右，不能向左
                    else -> false
                }
            check(!invalidDirection) { "蛇头正对着自己的身体，这样会撞到自己" }
        }
    }

    // 使用DFS查找哈密顿路径
    private fun findHamiltonianPath(start: Position): Boolean {
        val visited = mutableSetOf<Position>()

        fun dfs(current: Position): Boolean {
            visited.add(current)

            if (visited.size == segments.size) {
                return true
            }

            // 检查四个方向的相邻节点
            val neighbors =
                segments.filter { neighbor ->
                    neighbor !in visited &&
                        (
                            (abs(neighbor.x - current.x) == 1 && neighbor.y == current.y) ||
                                (abs(neighbor.y - current.y) == 1 && neighbor.x == current.x)
                        )
                }

            for (neighbor in neighbors) {
                if (dfs(neighbor)) {
                    return true
                }
            }

            visited.remove(current)
            return false
        }

        return dfs(start)
    }

    /**
     * 小蛇进食
     * @param foodPosition 食物位置
     * @param edible 食物的可食用属性
     * @return 进食结果
     */
    fun eat(
        foodPosition: Position,
        edible: Attributes.Edible,
    ): ActionResult {
        var lengthChange = edible.value
        // 如果食物没有长度变化效果，则禁止吃食
        if (lengthChange == 0) return ActionResult.Forbidden
        if (lengthChange > 0) {
            val isEatingSelf = segments.any { it == foodPosition }
            if (isEatingSelf) { // 自己的身体，参数错误
                return ActionResult.Forbidden
            }
            // 增加目标长度并在头部添加新节点
            targetLength += lengthChange
            segments.add(0, foodPosition)
        } else {
            lengthChange = abs(lengthChange)
            if (targetLength - lengthChange <= 0) { // 蛇的长度小于1等于死亡
                return ActionResult.Death
            }
            // 减少目标长度
            targetLength -= lengthChange
            if (segments.size > targetLength) { // 表示小蛇的当前渲染显示长度大于实际长度，要缩短
                val segmentsToRemove = segments.size - targetLength
                segments.removeAtRange(segments.size - segmentsToRemove, segmentsToRemove)
            }
        }
        return ActionResult.Success
    }

    /**
     * 移动小蛇
     * @param direction 移动方向
     * @return 是否成功移动
     */
    fun move(direction: Direction): Boolean {
        // 计算新的头部位置
        val newHeadPosition = segments[0] + direction.toOffsetPosition()
        // 检查是否与身体发生碰撞
        val hasCollision = segments.any { it == newHeadPosition }
        if (hasCollision) { // 蛇头和蛇身发生了碰撞，不能移动
            return false
        }
        // 更新蛇身各段的位置
        var currentPosition = newHeadPosition
        segments.forEachIndexed { index, position ->
            val nextPosition = segments[index]
            segments[index] = currentPosition
            currentPosition = nextPosition
        }
        // 如果目标长度大于当前长度，则在移动时增加一节
        if (targetLength > segments.size) { // 表示小蛇的当前渲染显示长度没有达到实际长度，所以每移动一步要多渲染一节
            segments.add(currentPosition)
        }
        return true
    }

    /**
     * 指定坐标是否在蛇的身体中
     */
    fun isPositionInSnakeBody(position: Position): Boolean = segments.contains(position)

    /**
     * 是否是蛇头
     */
    fun isPositionSnakeHead(position: Position): Boolean = segments[0] == position

    /**
     * 是否是蛇尾
     */
    fun isPositionSnakeTail(position: Position): Boolean = segments[segments.size - 1] == position

    /**
     * 获取指定坐标的蛇部位
     */
    fun getSnakePart(position: Position): SnakePart =
        when {
            isPositionSnakeHead(position) -> SnakePart.Head
            isPositionSnakeTail(position) -> SnakePart.Tail
            isPositionInSnakeBody(position) -> SnakePart.Body
            else -> SnakePart.None
        }
}

/**
 * MutableList的扩展函数，用于移除指定范围的元素
 * @param start 开始索引
 * @param length 要移除的元素个数
 */
private fun MutableList<*>.removeAtRange(
    start: Int,
    length: Int,
) {
    // 检查索引和长度是否有效，以防止越界异常
    if (start < 0 || start >= this.size || length <= 0) {
        return
    }

    // 计算移除的结束索引（不含）
    val end = minOf(start + length, this.size)

    // 从后往前移除，以避免索引变化问题
    for (i in end - 1 downTo start) {
        this.removeAt(i)
    }
}

/**
 * Direction的扩展函数，将方向转换为对应的位置偏移
 * @return 返回对应方向的位置偏移
 */
private fun Direction.toOffsetPosition(): Position =
    when (this) {
        Direction.Up -> Position(-1, 0) // 向上移动时行数减1
        Direction.Down -> Position(1, 0) // 向下移动时行数加1
        Direction.Left -> Position(0, -1) // 向左移动时列数减1
        Direction.Right -> Position(0, 1) // 向右移动时列数加1
    }
