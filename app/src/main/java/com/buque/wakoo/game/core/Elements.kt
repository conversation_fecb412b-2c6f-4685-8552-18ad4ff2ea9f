package com.buque.wakoo.game.core

/**
 * 游戏元素基础接口
 *
 * 定义了游戏中所有元素必须实现的基本属性：
 * @property id 元素的唯一标识符
 * @property name 元素的显示名称
 * @property desc 元素的描述文本
 * @property symbol 元素在游戏中显示的符号
 * @property attributes 元素具有的属性列表，如可食用、有重力等
 * @property editable 元素是否可编辑
 */
sealed interface IElement {
    val id: String
    val name: String
    val desc: String
    val symbol: String
    val attributes: List<Attributes>
    val editable: Boolean
}

/**
 * 元素
 */
data class Element(
    override val id: String,
    override val name: String,
    override val desc: String,
    override val symbol: String,
    override val attributes: List<Attributes> = emptyList(),
    override val editable: Boolean = false,
) : IElement

object Elements {
    val Empty =
        Element(
            id = "empty",
            name = "空格",
            desc = "空的地图格子，可以被其他元素占据",
            symbol = " ",
            attributes = emptyList(),
        )

    val Wall =
        Element(
            id = "wall",
            name = "墙壁",
            desc = "固定的障碍物，提供支撑",
            symbol = "🧱",
            attributes = listOf(Attributes.Support),
        )

    val Apple =
        Element(
            id = "apple",
            name = "苹果",
            desc = "普通食物，小蛇吃掉后长度+1",
            symbol = "🍎",
            attributes = listOf(Attributes.Edible(1), Attributes.Support),
        )

    val Spike =
        Element(
            id = "spike",
            name = "毒刺",
            desc = "危险的尖刺，接触后小蛇死亡",
            symbol = "🔺",
            attributes = listOf(Attributes.Deadly),
        )

    val Rock =
        Element(
            id = "rock",
            name = "石头",
            desc = "可推动的石头，有重力会下落",
            symbol = "🪨",
            attributes = listOf(Attributes.Gravity, Attributes.Support, Attributes.Pushable),
        )

    val Exit =
        Element(
            id = "exit",
            name = "出口",
            desc = "关卡出口，小蛇到达后通关",
            symbol = "🚪",
            attributes = listOf(Attributes.Support, Attributes.Victory),
        )

    fun createElement(
        id: String,
        name: String,
        desc: String,
        symbol: String,
        attributes: List<Attributes>,
        editable: Boolean = false,
    ) = Element(id, name, desc, symbol, attributes, editable)
}

data class SnakeBody(
    override val id: String = "snake_body",
    override val name: String = "蛇身",
    override val desc: String = "小蛇的身体部分",
    override val attributes: List<Attributes> = listOf(Attributes.Gravity, Attributes.Support),
    override val editable: Boolean = false,
) : IElement {
    override val symbol: String = "🟢"
}
