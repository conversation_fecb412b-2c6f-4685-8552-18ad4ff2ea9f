package com.buque.wakoo.game.core

/**
 * 地图完整性校验数据类
 * @property checksum 校验和
 * @property algorithm 校验算法
 * @property excludeFields 排除校验的字段列表
 */
data class Integrity(
    val checksum: String,
    val algorithm: String,
    val excludeFields: List<String>,
)

/**
 * 地图基本信息数据类
 * @property id 地图唯一标识
 * @property name 地图名称
 * @property description 地图描述
 * @property version 地图版本
 * @property coreVersion 核心版本
 * @property author 作者
 * @property createdDate 创建日期
 * @property updatedDate 更新日期
 */
data class MapInfo(
    val id: String,
    val name: String,
    val description: String,
    val version: String,
    val coreVersion: Int,
    val author: String,
    val createdDate: String,
    val updatedDate: String,
)

/**
 * 可编辑的关卡地图类
 * @property mapInfo 地图基本信息
 * @property snakeBodySegments 蛇身体段位置列表
 * @property snakeHeadDirection 蛇头朝向
 * @property gridElements 二维数组，存储元素id, 第一层表示每一列
 * @throws IllegalStateException 当蛇的操作不符合规则时抛出
 * @throws IndexOutOfBoundsException 当访问的位置超出地图边界时抛出
 */
class EditableLevelMap(
    private var mapInfo: MapInfo,
    private val snakeBodySegments: MutableList<Position>,
    private var snakeHeadDirection: Direction,
    private var gridElements: Array<Array<IElement>>,
) {
    /**
     * 在指定位置添加元素
     * @param elementPosition 元素位置
     * @param element 要添加的元素
     * @throws IllegalArgumentException 当尝试通过此方法添加蛇身时抛出
     * @throws IndexOutOfBoundsException 当位置超出地图边界时抛出
     */
    fun placeElementAt(
        elementPosition: Position,
        element: IElement,
    ) {
        if (element is SnakeBody) {
            throw IllegalArgumentException("请使用placeSnakeHead或placeSnakeBody方法添加小蛇")
        }
        gridElements[elementPosition.x][elementPosition.y] = element
    }

    /**
     * 获取指定位置的元素
     * @param elementPosition 元素位置
     * @return 返回该位置的元素
     * @throws IndexOutOfBoundsException 当位置超出地图边界时抛出
     */
    fun getElementAt(elementPosition: Position): IElement = gridElements[elementPosition.x][elementPosition.y]

    /**
     * 移除指定位置的元素
     * @param elementPosition 要移除元素的位置
     * @throws IllegalStateException 当尝试从非蛇尾位置移除蛇身时抛出
     * @throws IndexOutOfBoundsException 当位置超出地图边界时抛出
     */
    fun clearElementAt(elementPosition: Position) {
        if (snakeBodySegments.contains(elementPosition)) {
            if (snakeBodySegments.last() != elementPosition) {
                throw IllegalStateException("只能从蛇尾开始移除")
            }
            snakeBodySegments.remove(elementPosition)
        }
        gridElements[elementPosition.x][elementPosition.y] = Elements.Empty
    }

    /**
     * 设置蛇头位置和方向，要先添加蛇头再添加蛇身体
     * @param position 蛇头位置
     * @param direction 蛇头朝向
     * @throws IllegalStateException 当已存在蛇头时抛出
     * @throws IndexOutOfBoundsException 当位置超出地图边界时抛出
     */
    fun placeSnakeHead(
        position: Position,
        direction: Direction,
    ) {
        if (snakeBodySegments.isNotEmpty()) {
            throw IllegalStateException("只能设置一个蛇头")
        }
        snakeBodySegments.add(position)
        snakeHeadDirection = direction
    }

    /**
     * 设置蛇身体位置，设置之前要先添加蛇头
     * @param position 蛇身位置
     * @throws IllegalStateException 当未设置蛇头时抛出
     * @throws IndexOutOfBoundsException 当位置超出地图边界时抛出
     */
    fun placeSnakeBody(position: Position) {
        if (snakeBodySegments.isEmpty()) {
            throw IllegalStateException("请先设置蛇头")
        }
        snakeBodySegments.add(position)
    }

    /**
     * 改变地图大小并保留当前已添加的元素
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @return 如果调整成功返回true，如果尺寸未发生变化或无法调整则返回false
     * @throws IllegalArgumentException 当目标尺寸小于1时抛出
     */
    fun resize(
        targetWidth: Int,
        targetHeight: Int,
    ): Boolean {
        if (targetWidth < 1 || targetHeight < 1) {
            throw IllegalArgumentException("地图尺寸不能小于1")
        }

        val currentWidth = gridElements.size
        val currentHeight = gridElements[0].size

        // 判断是否发生变化
        if (targetWidth == currentWidth && targetHeight == currentHeight) {
            return false
        }

        //  缩小地图时只检查需要移除的区域
        if (targetWidth < currentWidth || targetHeight < currentHeight) {
            // 检查右边需要移除的区域
            if (targetWidth < currentWidth) {
                // 只遍历需要移除的列
                for (x in targetWidth until currentWidth) {
                    // 只检查到新高度和当前高度的较小值
                    for (y in 0 until minOf(currentHeight, targetHeight)) {
                        if (gridElements[x][y] != Elements.Empty) {
                            return false
                        }
                    }
                }
            }

            // 检查下边需要移除的区域
            if (targetHeight < currentHeight) {
                // 只遍历需要保留的列中需要移除的行
                for (x in 0 until minOf(currentWidth, targetWidth)) {
                    for (y in targetHeight until currentHeight) {
                        if (gridElements[x][y] != Elements.Empty) {
                            return false
                        }
                    }
                }
            }
        }

        // 创建新数组并复制元素
        val resizedGrid =
            Array(targetWidth) { x ->
                Array(targetHeight) { y ->
                    when {
                        x < currentWidth && y < currentHeight -> gridElements[x][y]
                        else -> Elements.Empty
                    }
                }
            }

        gridElements = resizedGrid
        return true
    }

    /**
     * 地图元素区域平移
     * @param startPos 区域起始位置
     * @param endPos 区域结束位置
     * @param offset 平移偏移量
     * @return 平移是否成功
     * @throws IndexOutOfBoundsException 当位置或偏移量导致越界时抛出
     * @throws IllegalArgumentException 当起始位置大于结束位置时抛出
     */
    fun moveArea(
        startPos: Position,
        endPos: Position,
        offset: Position,
    ): Boolean {
        // 检查区域是否有效
        if (startPos.x > endPos.x || startPos.y > endPos.y) {
            throw IllegalArgumentException("起始位置不能大于结束位置")
        }

        // 计算目标区域的边界
        val targetStartX = startPos.x + offset.x
        val targetStartY = startPos.y + offset.y
        val targetEndX = endPos.x + offset.x
        val targetEndY = endPos.y + offset.y

        // 检查是否超出地图边界
        if (targetStartX < 0 || targetStartY < 0 ||
            targetEndX >= gridElements.size ||
            targetEndY >= gridElements[0].size
        ) {
            throw IndexOutOfBoundsException("平移后区域超出地图边界")
        }

        // 检查源区域是否包含蛇的部分（不是全部）
        val snakePositions = snakeBodySegments
        val sourceContainsSnake =
            snakePositions.any { pos ->
                pos.x in startPos.x..endPos.x && pos.y in startPos.y..endPos.y
            }
        if (sourceContainsSnake) {
            // 检查是否包含完整的蛇
            val allSnakeInSource =
                snakePositions.all { pos ->
                    pos.x in startPos.x..endPos.x && pos.y in startPos.y..endPos.y
                }
            if (!allSnakeInSource) return false
        }

        // 检查目标区域中所有不与源区域重叠的部分是否有元素
        for (x in targetStartX..targetEndX) {
            for (y in targetStartY..targetEndY) {
                // 如果当前坐标在源区域内，跳过检查
                if (x in startPos.x..endPos.x && y in startPos.y..endPos.y) {
                    continue
                }
                val sourceX = x - offset.x
                val sourceY = y - offset.y
                // 只有源坐标有元素时才检查目标坐标
                if (gridElements[sourceX][sourceY] != Elements.Empty && gridElements[x][y] != Elements.Empty) {
                    return false
                }
            }
        }

        // 创建临时数组存储要移动的元素
        val tempElements =
            Array(endPos.x - startPos.x + 1) { x ->
                Array(endPos.y - startPos.y + 1) { y ->
                    gridElements[startPos.x + x][startPos.y + y]
                }
            }

        // 只清空源区域中不与目标区域重叠的部分
        for (x in startPos.x..endPos.x) {
            for (y in startPos.y..endPos.y) {
                if (x + offset.x !in startPos.x..endPos.x ||
                    y + offset.y !in startPos.y..endPos.y
                ) {
                    gridElements[x][y] = Elements.Empty
                }
            }
        }

        // 将元素移动到新位置
        for (x in 0 until tempElements.size) {
            for (y in 0 until tempElements[0].size) {
                gridElements[targetStartX + x][targetStartY + y] = tempElements[x][y]
            }
        }

        // 如果移动的区域包含蛇，更新蛇的位置
        if (sourceContainsSnake) {
            snakeBodySegments.forEachIndexed { index, position ->
                snakeBodySegments[index] = position + offset
            }
        }

        return true
    }

    /**
     * 保存地图
     * @throws IllegalStateException 当地图状态不合法时抛出，包括：
     * - 没有蛇或出口
     * - 开局即死亡或胜利
     * - 没有可行的获胜路径
     */
    fun save() {
    }
}
