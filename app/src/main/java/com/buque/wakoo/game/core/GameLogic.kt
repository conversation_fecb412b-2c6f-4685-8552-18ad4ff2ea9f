package com.buque.wakoo.game.core

object GameLogic {

    val coreVersion = 1

    fun validateMap() {
        // snake.validateSnake()
    }

    /**
     * 刷新所有具有[Attributes.Gravity]属性的元素的最后稳定的位置，并校验小蛇是否存活，下落过程中小蛇可能死亡
     */
    fun refreshGravityElements(): ActionResult {
        //
        return ActionResult.Success
    }

//    /**
//     * 检查位置是否在地图范围内
//     */
//    fun isPositionValid(position: Position): Boolean =
//        position.y in 0 until mapInfo.height &&
//            position.x in 0 until mapInfo.width

    /**
     * 移动蛇
     */
    fun moveSnake(direction: Direction): ActionResult {
//        val newHeadPosition =
//            when (direction) {
//                Direction.UP -> Position(snake.headPosition.y - 1, snake.headPosition.x)
//                Direction.DOWN -> Position(snake.headPosition.y + 1, snake.headPosition.x)
//                Direction.LEFT -> Position(snake.headPosition.y, snake.headPosition.x - 1)
//                Direction.RIGHT -> Position(snake.headPosition.y, snake.headPosition.x + 1)
//            }
//
//        if (!canMoveTo(newHeadPosition)) return false
//
//        // 更新蛇的位置
//        snake.bodyPositions.add(0, snake.headPosition)
//        if (snake.bodyPositions.size > snake.length) {
//            snake.bodyPositions.removeAt(snake.bodyPositions.size - 1)
//        }
//        snake.headPosition = newHeadPosition
//        snake.direction = direction

        return ActionResult.Success
    }

    /**
     * 检查位置是否可移动
     */
    private fun canMoveTo(position: Position): Boolean {
//        val element = getElementAt(position) ?: return false
//        return !element.attributes.contains(ElementAttribute.SUPPORT) ||
//            element.attributes.contains(ElementAttribute.EDIBLE)
        return false
    }

    /**
     * 检查是否胜利
     */
    private fun checkVictory(): Boolean {
//        val currentElement = getElementAt(snake.headPosition) ?: return false
//        return currentElement.attributes.contains(ElementAttribute.VICTORY)
        return false
    }

     /**
     * 校验地图完整性
     * @return 校验结果
     */
    fun validateIntegrity(): Boolean {
        return true
    }
}
