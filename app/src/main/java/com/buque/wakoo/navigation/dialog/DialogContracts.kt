package com.buque.wakoo.navigation.dialog

import androidx.compose.runtime.Composable
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties

typealias OnDialogContent = @Composable DialogScope.(Any) -> Unit

/**
 * 定义一个Dialog的目的地，即“要显示什么内容”。
 * 这是创建新Dialog时需要构建的对象。
 */
interface DialogDestination {
    /**
     * 一个可恢复的Dialog目的地。
     * 实现此接口的类必须可被`kotlinx.serialization`序列化。
     */
    interface Restorable : DialogDestination {
        val isRestorable: Boolean
            get() = true
    }

    /**
     * Dialog的Composable内容。
     * 它接收一个 `DialogScope` 作为上下文，用于在Dialog内部与控制器通信。
     */
    val content: OnDialogContent

    /**
     * 是否在配置发生变化时保留
     */
    val needKeepInConfigChange: Boolean
        get() = true

    val dialogProperties: AnyPopDialogProperties
        get() = AnyPopDialogProperties()
}

/**
 * 提供给Dialog内容的回调作用域，用于与外部控制器通信。
 */
interface DialogScope {
    /**
     * 当前Dialog的唯一ID。
     */
    val id: Int

    val dialogController: DialogController

    /**
     * 关闭当前Dialog，并可选地返回一个结果。
     * @param result 将返回给调用者的结果。
     */
    fun dismiss(
        result: Any? = null,
        tag: String? = null,
    )

    /**
     * 发送一个一次性的事件到事件总线。
     * @param tag 帮助标识。
     * @param event 要发送的事件。
     */
    fun sendEvent(
        event: DialogEvent,
        tag: String? = null,
    )
}

/**
 * 一个Dialog实例的句柄，允许调用者控制Dialog并等待其结果。
 * 这是与一个已显示的Dialog交互的主要方式。
 * await不建议使用，虽然方便，但是配置发生变化和进程重启的情况会丢失响应
 */
interface DialogHandle : DialogScope {
    /**
     * 异步等待Dialog关闭并返回其结果。
     * 如果Dialog被关闭时没有提供结果，则返回null。
     * 如果持有此句柄的ViewModel被销毁，此挂起函数将被取消。
     * await不建议使用，虽然方便，但是配置发生变化和进程重启的情况会丢失响应
     * 只有配置发生变化时保留不希望保留的dialog适合用这个方法[DialogDestination.needKeepInConfigChange==false]
     */
    suspend fun await(): Any?
}

/**
 * 用于事件总线的事件的标记接口。
 * 开发者可以创建自己的类来实现此接口，以定义不同类型的事件。
 */
interface DialogEvent

/**
 * 当一个Dialog被关闭且无法通过`CompletableDeferred`返回结果时（如进程重建后），
 * 会发送此事件。
 */
class DialogResultEvent(
    val result: Any?,
) : DialogEvent

interface DialogEventTag {
    val id: Int
    val tag: String?
}

/**
 * 一个DialogEvent包装
 * 主要是增加标识辅助识别
 */
class DialogEventWrapper(
    override val id: Int,
    override val tag: String?,
    val event: DialogEvent,
) : DialogEventTag
