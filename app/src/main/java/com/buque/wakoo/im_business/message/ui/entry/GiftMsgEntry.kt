package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.GiftWrapper
import com.buque.wakoo.bean.giftExtra
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im_business.message.MessageTheme
import com.buque.wakoo.im_business.message.MessageThemeBubble
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.manager.localizedFormatWithKey
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomUserInfoPanel
import com.buque.wakoo.ui.screens.messages.chat.PublishCpMedal
import com.buque.wakoo.ui.widget.ExpLevelWidget
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.UserLevelWidget
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.richtext.RichText

data class GiftMsgEntry(
    val sendUser: IMUser,
    val targetUser: User?,
    val giftModel: GiftWrapper,
) : MsgUIEntry

@Composable
fun GiftMsgEntry.RoomContent(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val isCN = !LocalSelfUserProvider.isJP

    Column(modifier = modifier.padding(end = 35.dp)) {
        Row {
            AvatarNetworkImage(
                user = sendUser,
                size = 28.dp,
                onClick = {
                    roomInfoState.sendEvent(
                        RoomEvent.PanelDialog {
                            LiveRoomUserInfoPanel(sendUser, roomInfoState)
                        },
                    )
                },
            )

            SizeWidth(8.dp)

            RichText(
                fontSize = 14.sp,
                color = Color.White,
                lineHeight = 26.sp,
                modifier = Modifier.padding(top = 1.dp),
            ) {
                append(sendUser.name)
                if (sendUser.isVip) {
                    append("  ")
                    InlineSizedContent(48.dp, 18.dp) {
                        VipCrownTag()
                    }
                }

                append(" ")
                if (isCN) {
                    InlineSizedContent(42.dp, 18.dp) {
                        UserLevelWidget(sendUser.level)
                    }
                } else {
                    InlineContent {
                        ExpLevelWidget(sendUser.wealthLevel, sendUser.charmLevel)
                    }
                }

                if (isCN) {
                    sendUser.cpUrl?.takeIf { it.isNotBlank() }?.also {
                        append(" ")
                        InlineSizedContent(64.dp, 18.dp) {
                            PublishCpMedal(
                                publicCp = BasicUser("", avatar = sendUser.cpAvatar.orEmpty()),
                                publicCpMedalUrl = it,
                                modifier =
                                    Modifier.size(64.dp, 18.dp),
                            )
                        }
                    }
                }

                sendUser.medalList?.forEach {
                    append(" ")
                    InlineSizedContent(it.width.dp, it.height.dp) {
                        NetworkImage(
                            data = it.icon,
                            modifier = Modifier.size(it.width.dp, it.height.dp),
                        )
                    }
                }
            }
        }

        MessageThemeBubble(
            defaultMsgTheme =
                MessageTheme(
                    painter = ColorPainter(Color(0x1AFFFFFF)),
                    paddingValues = PaddingValues(horizontal = 8.dp, vertical = 10.dp),
                    shape = RoundedCornerShape(8.dp),
                    contentColor = Color.White,
                    fontSize = 14.sp,
                    left = true,
                ),
            modifier = Modifier.padding(start = 36.dp),
        ) {
            RichText(
                lineHeight = 20.sp,
            ) {
                val receiverName = targetUser?.name ?: "所有人".localized
                val count = giftModel.count.coerceAtLeast(1).toString()
                val giftName = giftModel.gift.name

                val content =
                    if (giftModel.isLuckGift) {
                        val rewardUser =
                            when {
                                giftModel.receivers.size != 1 -> ""
                                giftModel.rewardObject == 1 -> "送礼人".localized
                                else -> "收礼人".localized
                            }

                        val rewardName =
                            when (giftModel.rewardType) {
                                2 -> "银币奖励".localized
                                else -> "钻石奖励".localized
                            }

                        if (giftModel.totalRewardCnt >= 0) {
                            if (giftModel.receivers.size < 2) {
                                "送给 %1\$s %2\$s个%3\$s，恭喜%4\$s获得了%5\$s个%6\$s".localizedFormatWithKey(
                                    "赠送福袋获得奖励",
                                    receiverName,
                                    giftModel.totalGiftCnt,
                                    giftName,
                                    rewardUser,
                                    giftModel.totalRewardCnt,
                                    rewardName,
                                )
                            } else {
                                "送给 %1\$s 等%2\$s人，%3\$s个%4\$s，恭喜%5\$s获得了%6\$s个%7\$s".localizedFormatWithKey(
                                    "赠送多人福袋获得奖励",
                                    receiverName,
                                    giftModel.receivers.size,
                                    giftModel.totalGiftCnt,
                                    giftName,
                                    rewardUser,
                                    giftModel.totalRewardCnt,
                                    rewardName,
                                )
                            }
                        } else {
                            if (giftModel.receivers.size < 2) {
                                "送给 %1\$s %2\$s个%3\$s，很遗憾%4\$s没有获得%5\$s".localizedFormatWithKey(
                                    "赠送福袋没有获得奖励",
                                    receiverName,
                                    giftModel.totalGiftCnt,
                                    giftName,
                                    rewardUser,
                                    rewardName,
                                )
                            } else {
                                "送给 %1\$s 等%2\$s人，%3\$s个%4\$s，很遗憾%5\$s没有获得6\$s".localizedFormatWithKey(
                                    "赠送多人福袋没有获得奖励",
                                    receiverName,
                                    giftModel.receivers.size,
                                    giftModel.totalGiftCnt,
                                    giftName,
                                    rewardUser,
                                    rewardName,
                                )
                            }
                        }
                    } else if (giftModel.isBlinxBox) {
                        "送给 %1\$s %2\$s个%3\$s，开出了限定礼物%4\$s".localizedFormat(
                            receiverName,
                            count,
                            giftModel.blindboxName.orEmpty(),
                            giftName,
                        )
                    } else {
                        "送给 %1\$s %2\$s个%3\$s".localizedFormat(
                            receiverName,
                            count,
                            giftName,
                        )
                    }

                withBuilder {
                    append(content)

                    var start = content.indexOf(receiverName, 0)
                    var end = start.plus(receiverName.length)

                    addStyle(SpanStyle(color = Color(0xFF66FE6B)), start, end)

                    if (giftModel.isBlinxBox) {
                        start = content.indexOf(giftName, end)
                        end = start.plus(giftName.length)
                        addStyle(SpanStyle(color = Color(0xFFFFEE56)), start, end)
                    } else if (giftModel.isLuckGift && giftModel.totalRewardCnt >= 0) {
                        start = content.lastIndexOf("${giftModel.totalRewardCnt}")
                        end = start.plus("${giftModel.totalRewardCnt}".length)
                        addStyle(SpanStyle(color = Color(0xFFFFEE56)), start, end)
                    }
                }

                if (!giftModel.isCupidLuckyGift) {
                    append(" ")
                    InlineSizedContent(20.dp, 20.dp) {
                        NetworkImage(data = giftModel.gift.icon)
                    }
                }

                append(" ")
            }
        }
    }
}

@Composable
fun GiftMsgEntry.C2CContent(modifier: Modifier = Modifier) {
    val isSelf = sendUser.isSelf
    CompositionLocalProvider(LocalLayoutDirection provides if (isSelf) LayoutDirection.Rtl else LayoutDirection.Ltr) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(end = 35.dp),
        ) {
            Spacer(modifier = Modifier.width(16.dp))
            AvatarNetworkImage(sendUser, size = 40.dp)
            Spacer(modifier = Modifier.width(8.dp))
            Column(modifier = Modifier.weight(1f, false)) {
                Row(verticalAlignment = Alignment.Bottom) {
                    CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
                        MessageThemeBubble(
                            defaultMsgTheme =
                                MessageTheme(
                                    painter = ColorPainter(Color.White),
                                    paddingValues = PaddingValues(12.dp),
                                    shape =
                                        RoundedCornerShape(
                                            topStart = if (sendUser.isSelf) 12.dp else 0.dp,
                                            topEnd = if (sendUser.isSelf) 0.dp else 12.dp,
                                            12.dp,
                                            12.dp,
                                        ),
                                    contentColor = Color(0xff111111),
                                    16.sp,
                                ),
                        ) {
                            Column {
                                Row(modifier = Modifier.width(220.dp), verticalAlignment = Alignment.CenterVertically) {
                                    NetworkImage(giftModel.gift.icon, modifier = Modifier.size(48.dp))
                                    Spacer(Modifier.width(12.dp))
                                    Column {
                                        Text(
                                            "送给%s".localizedFormat(giftModel.receiverName),
                                            maxLines = 1,
                                            fontSize = 16.sp,
                                            lineHeight = 16.sp,
                                            overflow = TextOverflow.Ellipsis,
                                            color = Color(0xff1d2129),
                                        )
                                        Spacer(modifier = Modifier.height(12.dp))
                                        Text(
                                            "${giftModel.gift.name} x${giftModel.count}",
                                            color = Color(0xff86909c),
                                            fontSize = 12.sp,
                                        )
                                    }
                                }

                                // 显示礼物额外信息（新增功能）
                                val giftExtraInfo =
                                    remember(giftModel) {
                                        // 3表示私聊场景
                                        giftModel.giftExtra(GiftWrapper.PRIVATE)
                                    }
                                if (giftExtraInfo.isNotBlank()) {
                                    Text(
                                        text = giftExtraInfo,
                                        modifier = Modifier.padding(top = 12.dp),
                                        color = Color(0xFFFF9300),
                                        fontSize = 12.sp,
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
