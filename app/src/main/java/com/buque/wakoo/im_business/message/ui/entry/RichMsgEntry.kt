package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.buque.wakoo.bean.RichItem
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.inlineTextContent
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im_business.message.MessageTheme
import com.buque.wakoo.im_business.message.MessageThemeBubble
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomUserInfoPanel
import com.buque.wakoo.ui.screens.messages.chat.PublishCpMedal
import com.buque.wakoo.ui.widget.ExpLevelWidget
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.UserLevelWidget
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.richtext.RichText

data class RichMsgEntry(
    val sendUser: IMUser,
    val richText: List<RichItem>,
) : MsgUIEntry


@Composable
fun RichMsgEntry.RoomContent(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val user = this.sendUser
    val density = LocalDensity.current
    val fontSize =
        with(density) {
            14.dp.toPx().toSp()
        }

    val lineHeight =
        with(density) {
            26.dp.toPx().toSp()
        }

    val isCN = !LocalSelfUserProvider.isJP

    Column(modifier = modifier.padding(end = 35.dp)) {
        Row {
            AvatarNetworkImage(
                user = user,
                size = 28.dp,
                onClick = {
                    roomInfoState.sendEvent(
                        RoomEvent.PanelDialog {
                            LiveRoomUserInfoPanel(user, roomInfoState)
                        },
                    )
                },
            )

            SizeWidth(8.dp)

            RichText(
                fontSize = fontSize,
                color = Color.White,
                lineHeight = lineHeight,
                modifier = Modifier.padding(top = 1.dp),
            ) {
                append(user.name)
                if (user.isVip) {
                    append("  ")
                    InlineSizedContent(48.dp, 18.dp) {
                        VipCrownTag()
                    }
                }

                append(" ")
                if (isCN) {
                    InlineSizedContent(42.dp, 18.dp) {
                        UserLevelWidget(user.level)
                    }
                } else {
                    InlineContent {
                        ExpLevelWidget(user.wealthLevel, user.charmLevel)
                    }
                }

                if (isCN) {
                    user.cpUrl?.takeIf { it.isNotBlank() }?.also {
                        append(" ")
                        InlineSizedContent(64.dp, 18.dp) {
                            PublishCpMedal(
                                publicCp = BasicUser("", avatar = user.cpAvatar.orEmpty()),
                                publicCpMedalUrl = it,
                                modifier =
                                    Modifier.size(64.dp, 18.dp),
                            )
                        }
                    }
                }

                user.medalList?.forEach {
                    append(" ")
                    InlineSizedContent(it.width.dp, it.height.dp) {
                        NetworkImage(
                            data = it.icon,
                            modifier = Modifier.size(it.width.dp, it.height.dp),
                        )
                    }
                }
            }
        }

        val density = LocalDensity.current
        MessageThemeBubble(
            chatBubble = user.chatBubble,
            defaultMsgTheme =
                MessageTheme(
                    painter = ColorPainter(Color(0x1AFFFFFF)),
                    paddingValues = PaddingValues(horizontal = 8.dp, vertical = 10.dp),
                    shape = RoundedCornerShape(8.dp),
                    contentColor = Color.White,
                    fontSize = fontSize,
                    left = true,
                ),
            modifier = Modifier.padding(start = 36.dp),
        ) {
            RichText {
                richText.forEach { item ->
                    if (item.isText) {
                        withBuilder {
                            append(item.richString)
                        }
                    }
                    if (item.isIcon) {
                        inlineTextContent(item.icon, density, item.width, item.height) {
                            NetworkImage(item.icon, contentScale = ContentScale.FillBounds)
                        }
                    }
                }
            }
        }
    }
}